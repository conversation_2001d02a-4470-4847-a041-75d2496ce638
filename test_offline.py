"""Test script that works without OpenAI API calls."""

import sqlite3
import os
from pathlib import Path

def test_database_queries():
    """Test database functionality without AI."""
    print("🧪 Testing Database Queries (No AI)...")
    
    try:
        conn = sqlite3.connect('database/chatbot.db')
        cursor = conn.cursor()
        
        # Test basic queries
        queries = [
            ("Total employees", "SELECT COUNT(*) as count FROM employees"),
            ("Departments", "SELECT DISTINCT department FROM employees"),
            ("Average salary", "SELECT AVG(salary) as avg_salary FROM employees"),
            ("Sales count", "SELECT COUNT(*) as sales_count FROM sales"),
            ("Revenue total", "SELECT SUM(total_amount) as total_revenue FROM sales")
        ]
        
        for name, query in queries:
            cursor.execute(query)
            result = cursor.fetchone()
            print(f"✅ {name}: {result}")
        
        conn.close()
        return True
        
    except Exception as e:
        print(f"❌ Database test error: {e}")
        return False

def test_document_processing():
    """Test document processing without embeddings."""
    print("\n🧪 Testing Document Processing (No AI)...")
    
    try:
        from agents.rag_agent import RAGAgent
        
        # Create RAG agent but don't initialize embeddings
        rag_agent = RAGAgent.__new__(RAGAgent)  # Create without __init__
        
        # Test PDF text extraction
        docs_path = Path("documents")
        pdf_files = list(docs_path.glob("*.pdf"))
        
        if pdf_files:
            print(f"✅ Found {len(pdf_files)} PDF files")
            
            # Test text extraction from first PDF
            pdf_path = pdf_files[0]
            text = rag_agent.extract_text_from_pdf(str(pdf_path))
            
            if text and len(text) > 100:
                print(f"✅ Successfully extracted {len(text)} characters from {pdf_path.name}")
                print(f"   Sample: {text[:100]}...")
                return True
            else:
                print(f"⚠️ Limited text extracted from {pdf_path.name}")
                return True
        else:
            print("⚠️ No PDF files found")
            return True
            
    except Exception as e:
        print(f"❌ Document processing error: {e}")
        return False

def test_system_structure():
    """Test system file structure."""
    print("\n🧪 Testing System Structure...")
    
    required_files = [
        "config/settings.py",
        "agents/main_agent.py",
        "agents/rag_agent.py", 
        "agents/sql_agent.py",
        "agents/intent_classifier.py",
        "ui/streamlit_app.py",
        "database/chatbot.db",
        ".env"
    ]
    
    required_dirs = [
        "documents",
        "agents",
        "config",
        "ui",
        "database"
    ]
    
    all_good = True
    
    for file_path in required_files:
        if os.path.exists(file_path):
            print(f"✅ {file_path}")
        else:
            print(f"❌ Missing: {file_path}")
            all_good = False
    
    for dir_path in required_dirs:
        if os.path.isdir(dir_path):
            print(f"✅ {dir_path}/")
        else:
            print(f"❌ Missing directory: {dir_path}/")
            all_good = False
    
    return all_good

def test_imports_basic():
    """Test basic imports without initializing AI components."""
    print("\n🧪 Testing Basic Imports...")
    
    try:
        # Test config
        from config.settings import settings
        print("✅ Settings imported")
        
        # Test that we can import agent classes
        from agents.rag_agent import RAGAgent
        from agents.sql_agent import SQLAgent
        from agents.intent_classifier import IntentClassifier
        from agents.main_agent import MainAgent
        print("✅ All agent classes imported")
        
        # Test database libraries
        import sqlite3
        import pandas as pd
        print("✅ Database libraries imported")
        
        # Test document processing
        import PyPDF2
        print("✅ PDF processing library imported")
        
        return True
        
    except Exception as e:
        print(f"❌ Import error: {e}")
        return False

def show_sample_data():
    """Show sample data from database."""
    print("\n📊 Sample Data Preview:")
    
    try:
        conn = sqlite3.connect('database/chatbot.db')
        
        # Show sample employees
        print("\n👥 Sample Employees:")
        cursor = conn.cursor()
        cursor.execute("SELECT name, department, position, salary FROM employees LIMIT 5")
        employees = cursor.fetchall()
        for emp in employees:
            print(f"  {emp[0]} - {emp[1]} - {emp[2]} - ${emp[3]:,.0f}")
        
        # Show sample sales
        print("\n💰 Sample Sales:")
        cursor.execute("SELECT product_name, category, total_amount, sale_date FROM sales LIMIT 5")
        sales = cursor.fetchall()
        for sale in sales:
            print(f"  {sale[0]} ({sale[1]}) - ${sale[2]:,.2f} on {sale[3]}")
        
        conn.close()
        
    except Exception as e:
        print(f"Error showing sample data: {e}")

def main():
    """Run offline tests."""
    print("🤖 Agentic Chatbot - Offline System Test")
    print("=" * 50)
    print("This test runs without making OpenAI API calls")
    print("=" * 50)
    
    tests = [
        ("System Structure", test_system_structure),
        ("Basic Imports", test_imports_basic),
        ("Database Queries", test_database_queries),
        ("Document Processing", test_document_processing),
    ]
    
    results = {}
    
    for test_name, test_func in tests:
        print(f"\n{'='*20} {test_name} {'='*20}")
        results[test_name] = test_func()
    
    # Show sample data
    show_sample_data()
    
    # Summary
    print("\n" + "=" * 50)
    print("📊 OFFLINE TEST SUMMARY")
    print("=" * 50)
    
    passed = sum(results.values())
    total = len(results)
    
    for test_name, result in results.items():
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{test_name:20} {status}")
    
    print(f"\nOverall: {passed}/{total} tests passed")
    
    if passed == total:
        print("\n🎉 System structure is perfect!")
        print("💡 Once you add OpenAI billing, everything will work!")
        print("\n🚀 To start the chatbot after adding billing:")
        print("   python run_chatbot.py")
    else:
        print("\n⚠️ Some structural issues found. Please check above.")
    
    print("\n💳 To fix the OpenAI quota issue:")
    print("   1. Go to https://platform.openai.com/account/billing")
    print("   2. Add a payment method")
    print("   3. Set a usage limit (e.g., $10-20)")
    print("   4. Wait 5-10 minutes for activation")

if __name__ == "__main__":
    main()
