"""Simple test for SQL functionality."""

def test_sql_simple():
    """Test SQL functionality directly."""
    import sqlite3
    
    print("🧪 Testing Direct SQL Queries")
    print("=" * 40)
    
    try:
        conn = sqlite3.connect('database/chatbot.db')
        cursor = conn.cursor()
        
        # Test simple queries
        queries = [
            ("Employee count", "SELECT COUNT(*) as count FROM employees"),
            ("Average salary", "SELECT AVG(salary) as avg_salary FROM employees"),
            ("Department breakdown", "SELECT department, COUNT(*) as count FROM employees GROUP BY department"),
        ]
        
        for name, query in queries:
            print(f"\n📊 {name}:")
            print(f"SQL: {query}")
            cursor.execute(query)
            results = cursor.fetchall()
            print(f"Results: {results}")
        
        conn.close()
        print("\n✅ Direct SQL queries work!")
        
    except Exception as e:
        print(f"❌ SQL error: {e}")

def test_main_agent_sql():
    """Test main agent SQL routing."""
    from agents.main_agent import get_main_agent
    
    print("\n🤖 Testing Main Agent SQL Routing")
    print("=" * 40)
    
    agent = get_main_agent()
    
    # Test a simple SQL query
    query = "How many employees are there?"
    print(f"\n🔍 Query: {query}")
    
    try:
        result = agent.process_query(query)
        print(f"Response keys: {list(result.keys())}")
        print(f"Intent: {result.get('intent', 'N/A')}")
        print(f"Agent: {result.get('agent_used', 'N/A')}")
        print(f"Answer: {result.get('answer', 'N/A')[:100]}...")
        
    except Exception as e:
        print(f"❌ Error: {e}")

if __name__ == "__main__":
    test_sql_simple()
    test_main_agent_sql()
