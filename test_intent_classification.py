"""Test the improved intent classification."""

def test_intent_classification():
    """Test the enhanced intent classification."""
    from agents.main_agent import get_main_agent
    
    agent = get_main_agent()
    
    # Test queries
    test_queries = [
        # SQL queries
        ("How many employees are there?", "SQL"),
        ("What is the average salary?", "SQL"),
        ("What is the average salary by department?", "SQL"),
        ("How many sales were made in Q4 2024?", "SQL"),
        ("Which region has the highest total sales?", "SQL"),
        ("Show me the top 5 performing employees", "SQL"),
        ("What is the total revenue?", "SQL"),
        ("Count of customers", "SQL"),
        ("Statistics about sales", "SQL"),
        
        # RAG queries
        ("What is the company's vacation policy?", "RAG"),
        ("How do I set up the SmartHome AI Assistant?", "RAG"),
        ("Tell me about the company benefits package", "RAG"),
        ("What is the code of conduct?", "RAG"),
        ("How to install the device?", "RAG"),
        ("What are the latest advances in AI?", "RAG"),
        ("Explain the company handbook", "RAG"),
        ("What is the user manual about?", "RAG"),
    ]
    
    print("🧪 Testing Enhanced Intent Classification")
    print("=" * 50)
    
    correct = 0
    total = len(test_queries)
    
    for query, expected in test_queries:
        intent, confidence, reasoning = agent._simple_intent_classification(query)
        
        status = "✅" if intent == expected else "❌"
        print(f"{status} Query: {query}")
        print(f"   Expected: {expected}, Got: {intent} (confidence: {confidence:.2f})")
        print(f"   Reasoning: {reasoning}")
        print()
        
        if intent == expected:
            correct += 1
    
    print("=" * 50)
    print(f"📊 Results: {correct}/{total} correct ({correct/total*100:.1f}%)")
    
    if correct/total >= 0.8:
        print("🎉 Intent classification is working well!")
    else:
        print("⚠️ Intent classification needs improvement")

if __name__ == "__main__":
    test_intent_classification()
