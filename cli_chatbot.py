"""Command-line interface for the Agentic Chatbot."""

import os
import sys
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

def setup_system():
    """Setup the chatbot system."""
    print("🤖 Agentic Chatbot - CLI Version")
    print("=" * 40)
    
    # Check environment
    api_key = os.getenv('OPENAI_API_KEY')
    if not api_key or api_key == 'your_openai_api_key_here':
        print("❌ OpenAI API key not set!")
        print("Please set your OPENAI_API_KEY in the .env file.")
        return None
    
    try:
        from agents.main_agent import get_main_agent
        
        print("Initializing agents...")
        main_agent = get_main_agent()
        
        print("Initializing RAG system...")
        result = main_agent.initialize_rag_system()
        
        if result["success"]:
            print("✅ System ready!")
        else:
            print(f"⚠️ RAG initialization warning: {result['message']}")
        
        return main_agent
        
    except Exception as e:
        print(f"❌ Error initializing system: {e}")
        return None

def print_help():
    """Print help information."""
    print("\n📚 Sample Queries:")
    print("\nSQL/Statistical Queries:")
    print("  • What is the average salary by department?")
    print("  • How many sales were made in Q4 2024?")
    print("  • Which region has the highest total sales?")
    print("  • Show me the top 5 performing employees")
    
    print("\nRAG/Document Queries:")
    print("  • What is the company's vacation policy?")
    print("  • How do I set up the SmartHome AI Assistant?")
    print("  • What are the latest advances in large language models?")
    print("  • Tell me about the company benefits package")
    
    print("\nCommands:")
    print("  • 'help' - Show this help")
    print("  • 'status' - Show system status")
    print("  • 'quit' or 'exit' - Exit the chatbot")

def format_response(response):
    """Format response for CLI display."""
    print(f"\n🤖 Agent: {response.get('agent_used', 'Unknown')}")
    print(f"📝 Answer: {response.get('answer', 'No answer provided')}")
    
    if response.get('agent_used') == 'SQL':
        sql_query = response.get('sql_query')
        if sql_query:
            print(f"🔍 SQL Query: {sql_query}")
        
        row_count = response.get('row_count', 0)
        if row_count > 0:
            print(f"📊 Rows returned: {row_count}")
    
    elif response.get('agent_used') == 'RAG':
        sources = response.get('sources', [])
        if sources:
            print(f"📚 Sources: {', '.join(sources)}")
        
        chunks = response.get('retrieved_chunks', 0)
        if chunks > 0:
            print(f"📄 Document chunks: {chunks}")
    
    confidence = response.get('confidence', 0)
    if confidence > 0:
        print(f"🎯 Confidence: {confidence:.2f}")
    
    reasoning = response.get('reasoning', '')
    if reasoning:
        print(f"💭 Reasoning: {reasoning}")

def main():
    """Main CLI loop."""
    # Setup system
    main_agent = setup_system()
    if not main_agent:
        return
    
    print("\n💬 Chat started! Type 'help' for sample queries or 'quit' to exit.")
    print_help()
    
    while True:
        try:
            # Get user input
            user_input = input("\n👤 You: ").strip()
            
            if not user_input:
                continue
            
            # Handle commands
            if user_input.lower() in ['quit', 'exit', 'q']:
                print("👋 Goodbye!")
                break
            
            elif user_input.lower() == 'help':
                print_help()
                continue
            
            elif user_input.lower() == 'status':
                status = main_agent.get_system_status()
                print(f"\n📊 System Status: {status['status']}")
                print(f"🤖 RAG Agent: {status['agents']['rag']['status']}")
                print(f"🗃️ SQL Agent: {status['agents']['sql']['status']}")
                print(f"💬 Conversation History: {status['conversation_history_count']} messages")
                continue
            
            # Process query
            print("\n🔄 Processing...")
            response = main_agent.process_query(user_input)
            
            # Display response
            format_response(response)
            
        except KeyboardInterrupt:
            print("\n\n👋 Goodbye!")
            break
        except Exception as e:
            print(f"\n❌ Error: {e}")

if __name__ == "__main__":
    main()
