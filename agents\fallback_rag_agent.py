"""Fallback RAG Agent that works without OpenAI embeddings."""

import os
from typing import List, Dict, Any
from pathlib import Path
import PyPDF2
import re
from config.settings import settings

class FallbackRAGAgent:
    """RAG Agent that uses text search instead of embeddings when OpenAI is unavailable."""
    
    def __init__(self):
        """Initialize the fallback RAG agent."""
        self.documents = []
        self.chunks = []
        self.load_and_process_documents()
    
    def extract_text_from_pdf(self, pdf_path: str) -> str:
        """Extract text from a PDF file."""
        text = ""
        try:
            with open(pdf_path, 'rb') as file:
                pdf_reader = PyPDF2.PdfReader(file)
                for page in pdf_reader.pages:
                    text += page.extract_text() + "\n"
        except Exception as e:
            print(f"Error extracting text from {pdf_path}: {e}")
        return text
    
    def load_and_process_documents(self):
        """Load and process documents for text search."""
        documents_path = Path("documents")
        
        if not documents_path.exists():
            print(f"Documents directory does not exist")
            return
        
        for file_path in documents_path.glob("*.pdf"):
            print(f"Loading: {file_path}")
            text = self.extract_text_from_pdf(str(file_path))
            
            if text.strip():
                # Split into chunks
                chunks = self.simple_text_splitter(text, chunk_size=1000, overlap=200)
                
                for i, chunk in enumerate(chunks):
                    self.chunks.append({
                        'content': chunk,
                        'source': str(file_path),
                        'filename': file_path.name,
                        'chunk_id': i
                    })
        
        print(f"Loaded {len(self.chunks)} text chunks from {len(list(Path('documents').glob('*.pdf')))} documents")
    
    def simple_text_splitter(self, text: str, chunk_size: int = 1000, overlap: int = 200) -> List[str]:
        """Simple text splitter that doesn't require external libraries."""
        chunks = []
        start = 0
        
        while start < len(text):
            end = start + chunk_size
            
            # Try to break at sentence boundary
            if end < len(text):
                # Look for sentence endings
                sentence_end = text.rfind('.', start, end)
                if sentence_end > start + chunk_size // 2:
                    end = sentence_end + 1
            
            chunk = text[start:end].strip()
            if chunk:
                chunks.append(chunk)
            
            start = end - overlap
            if start >= len(text):
                break
        
        return chunks
    
    def search_documents(self, query: str, max_results: int = 5) -> List[Dict[str, Any]]:
        """Search documents using simple text matching."""
        query_lower = query.lower()
        query_words = re.findall(r'\w+', query_lower)
        
        scored_chunks = []
        
        for chunk in self.chunks:
            content_lower = chunk['content'].lower()
            score = 0
            
            # Score based on word matches
            for word in query_words:
                if len(word) > 2:  # Skip very short words
                    count = content_lower.count(word)
                    score += count * len(word)  # Longer words get higher weight
            
            # Bonus for exact phrase matches
            if query_lower in content_lower:
                score += len(query) * 2
            
            if score > 0:
                scored_chunks.append({
                    'content': chunk['content'],
                    'metadata': {
                        'source': chunk['source'],
                        'filename': chunk['filename'],
                        'chunk_id': chunk['chunk_id']
                    },
                    'score': score
                })
        
        # Sort by score and return top results
        scored_chunks.sort(key=lambda x: x['score'], reverse=True)
        return scored_chunks[:max_results]
    
    def generate_simple_response(self, query: str, retrieved_docs: List[Dict[str, Any]]) -> str:
        """Generate a simple response using retrieved documents."""
        if not retrieved_docs:
            return "I couldn't find any relevant information in the documents to answer your question."
        
        # Find the most relevant content
        best_match = retrieved_docs[0]
        content = best_match['content']
        
        # Try to extract relevant sentences
        sentences = re.split(r'[.!?]+', content)
        relevant_sentences = []
        
        query_words = re.findall(r'\w+', query.lower())
        
        for sentence in sentences:
            sentence = sentence.strip()
            if len(sentence) > 20:  # Skip very short sentences
                sentence_lower = sentence.lower()
                word_matches = sum(1 for word in query_words if word in sentence_lower)
                
                if word_matches >= 2 or any(word in sentence_lower for word in query_words if len(word) > 4):
                    relevant_sentences.append(sentence)
        
        if relevant_sentences:
            response = ". ".join(relevant_sentences[:3])  # Take first 3 relevant sentences
            if not response.endswith('.'):
                response += '.'
            return response
        else:
            # Fallback to first part of best matching chunk
            return content[:500] + "..." if len(content) > 500 else content
    
    def query(self, question: str) -> Dict[str, Any]:
        """Main method to handle RAG queries using text search."""
        print(f"Fallback RAG Agent processing query: {question}")
        
        # Search for relevant documents
        retrieved_docs = self.search_documents(question)
        
        if not retrieved_docs:
            return {
                "answer": "I couldn't find any relevant information in the documents to answer your question.",
                "sources": [],
                "confidence": 0.0,
                "method": "text_search"
            }
        
        # Generate response
        answer = self.generate_simple_response(question, retrieved_docs)
        
        # Extract sources
        sources = list(set([
            doc['metadata'].get('filename', 'Unknown')
            for doc in retrieved_docs
        ]))
        
        # Calculate simple confidence based on number of matches
        confidence = min(0.9, len(retrieved_docs) * 0.2)
        
        return {
            "answer": answer,
            "sources": sources,
            "confidence": confidence,
            "retrieved_chunks": len(retrieved_docs),
            "method": "text_search"
        }

# Global fallback agent instance
fallback_rag_agent = None

def get_fallback_rag_agent():
    """Get or create fallback RAG agent instance."""
    global fallback_rag_agent
    if fallback_rag_agent is None:
        fallback_rag_agent = FallbackRAGAgent()
    return fallback_rag_agent
