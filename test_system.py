"""Test script to verify all system components work correctly."""

import os
import sys
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

def test_environment():
    """Test environment setup."""
    print("🧪 Testing Environment Setup...")
    
    # Check OpenAI API key
    api_key = os.getenv('OPENAI_API_KEY')
    if not api_key or api_key == 'your_openai_api_key_here':
        print("❌ OpenAI API key not set properly")
        return False
    
    print("✅ Environment variables loaded")
    return True

def test_imports():
    """Test all required imports."""
    print("\n🧪 Testing Imports...")
    
    try:
        from config.settings import settings
        print("✅ Config imported")
        
        from agents.intent_classifier import get_intent_classifier
        print("✅ Intent classifier imported")
        
        from agents.rag_agent import get_rag_agent
        print("✅ RAG agent imported")
        
        from agents.sql_agent import get_sql_agent
        print("✅ SQL agent imported")
        
        from agents.main_agent import get_main_agent
        print("✅ Main agent imported")
        
        return True
        
    except Exception as e:
        print(f"❌ Import error: {e}")
        return False

def test_intent_classification():
    """Test intent classification."""
    print("\n🧪 Testing Intent Classification...")
    
    try:
        from agents.intent_classifier import classify_query
        
        # Test SQL query
        sql_result = classify_query("What is the average salary by department?")
        print(f"SQL Query Classification: {sql_result['intent']} (confidence: {sql_result['confidence']:.2f})")
        
        # Test RAG query
        rag_result = classify_query("What is the company vacation policy?")
        print(f"RAG Query Classification: {rag_result['intent']} (confidence: {rag_result['confidence']:.2f})")
        
        if sql_result['intent'] == 'SQL' and rag_result['intent'] == 'RAG':
            print("✅ Intent classification working correctly")
            return True
        else:
            print("❌ Intent classification not working as expected")
            return False
            
    except Exception as e:
        print(f"❌ Intent classification error: {e}")
        return False

def test_sql_agent():
    """Test SQL agent functionality."""
    print("\n🧪 Testing SQL Agent...")
    
    try:
        from agents.sql_agent import get_sql_agent
        
        sql_agent = get_sql_agent()
        
        # Test simple query
        result = sql_agent.query("How many employees are there?")
        
        if result['success']:
            print("✅ SQL agent query successful")
            print(f"Sample response: {result['answer'][:100]}...")
            return True
        else:
            print(f"❌ SQL agent query failed: {result['answer']}")
            return False
            
    except Exception as e:
        print(f"❌ SQL agent error: {e}")
        return False

def test_rag_agent():
    """Test RAG agent functionality."""
    print("\n🧪 Testing RAG Agent...")
    
    try:
        from agents.rag_agent import get_rag_agent
        
        rag_agent = get_rag_agent()
        
        # Initialize if needed
        print("Initializing RAG system...")
        rag_agent.index_documents()
        
        # Test query
        result = rag_agent.query("What is the company vacation policy?")
        
        if result['answer'] and not result['answer'].startswith("I couldn't find"):
            print("✅ RAG agent query successful")
            print(f"Sample response: {result['answer'][:100]}...")
            print(f"Sources: {result.get('sources', [])}")
            return True
        else:
            print("⚠️ RAG agent returned no results (documents may not be indexed)")
            print(f"Response: {result['answer']}")
            return True  # This is okay if no documents are available
            
    except Exception as e:
        print(f"❌ RAG agent error: {e}")
        return False

def test_main_agent():
    """Test main agent orchestration."""
    print("\n🧪 Testing Main Agent Orchestration...")
    
    try:
        from agents.main_agent import get_main_agent
        
        main_agent = get_main_agent()
        
        # Test SQL query through main agent
        sql_response = main_agent.process_query("How many employees work in Engineering?")
        print(f"SQL Query Result: {sql_response['agent_used']} agent used")
        
        # Test RAG query through main agent
        rag_response = main_agent.process_query("What are the company benefits?")
        print(f"RAG Query Result: {rag_response['agent_used']} agent used")
        
        # Check system status
        status = main_agent.get_system_status()
        print(f"System Status: {status['status']}")
        
        print("✅ Main agent orchestration working")
        return True
        
    except Exception as e:
        print(f"❌ Main agent error: {e}")
        return False

def test_database():
    """Test database connectivity and data."""
    print("\n🧪 Testing Database...")
    
    try:
        import sqlite3
        
        conn = sqlite3.connect('database/chatbot.db')
        cursor = conn.cursor()
        
        # Check tables exist
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table';")
        tables = cursor.fetchall()
        
        if len(tables) >= 4:  # Should have employees, sales, customer_satisfaction, financial_metrics
            print(f"✅ Database has {len(tables)} tables")
            
            # Check sample data
            cursor.execute("SELECT COUNT(*) FROM employees;")
            emp_count = cursor.fetchone()[0]
            print(f"✅ Employees table has {emp_count} records")
            
            conn.close()
            return True
        else:
            print(f"❌ Database missing tables. Found: {[t[0] for t in tables]}")
            conn.close()
            return False
            
    except Exception as e:
        print(f"❌ Database error: {e}")
        return False

def test_documents():
    """Test document availability."""
    print("\n🧪 Testing Documents...")
    
    try:
        import os
        from pathlib import Path
        
        docs_path = Path("documents")
        if docs_path.exists():
            pdf_files = list(docs_path.glob("*.pdf"))
            if pdf_files:
                print(f"✅ Found {len(pdf_files)} PDF documents")
                for pdf in pdf_files:
                    print(f"  - {pdf.name}")
                return True
            else:
                print("⚠️ No PDF documents found in documents/ folder")
                return True  # Not critical for basic testing
        else:
            print("⚠️ Documents folder not found")
            return True  # Not critical for basic testing
            
    except Exception as e:
        print(f"❌ Documents test error: {e}")
        return False

def run_all_tests():
    """Run all tests."""
    print("🚀 Starting System Tests")
    print("=" * 50)
    
    tests = [
        ("Environment", test_environment),
        ("Imports", test_imports),
        ("Database", test_database),
        ("Documents", test_documents),
        ("Intent Classification", test_intent_classification),
        ("SQL Agent", test_sql_agent),
        ("RAG Agent", test_rag_agent),
        ("Main Agent", test_main_agent),
    ]
    
    results = {}
    
    for test_name, test_func in tests:
        try:
            results[test_name] = test_func()
        except Exception as e:
            print(f"❌ {test_name} test failed with exception: {e}")
            results[test_name] = False
    
    # Summary
    print("\n" + "=" * 50)
    print("📊 TEST SUMMARY")
    print("=" * 50)
    
    passed = sum(results.values())
    total = len(results)
    
    for test_name, result in results.items():
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{test_name:20} {status}")
    
    print(f"\nOverall: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! Your system is ready to use.")
        print("\nTo start the chatbot, run: python run_chatbot.py")
    else:
        print("⚠️ Some tests failed. Please check the errors above.")
        
        if not results.get("Environment", False):
            print("\n💡 Tip: Make sure to set your OpenAI API key in the .env file")
        
        if not results.get("Database", False):
            print("\n💡 Tip: Run 'python database/create_sample_db.py' to create the database")
        
        if not results.get("Documents", False):
            print("\n💡 Tip: Run 'python data/create_sample_pdfs.py' to create sample documents")

if __name__ == "__main__":
    run_all_tests()
