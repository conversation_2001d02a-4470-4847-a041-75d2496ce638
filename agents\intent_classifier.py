"""Intent Classification for routing queries to appropriate agents."""

from typing import Dict, Any
from langchain_openai import <PERSON>t<PERSON>penA<PERSON>
from config.settings import settings

class IntentClassifier:
    """Classifier to determine which agent should handle a query."""
    
    def __init__(self):
        """Initialize the intent classifier."""
        self.llm = ChatOpenAI(
            openai_api_key=settings.openai_api_key,
            model_name=settings.chat_model,
            temperature=0.1,  # Low temperature for consistent classification
            max_tokens=200
        )
    
    def classify_intent(self, query: str) -> Dict[str, Any]:
        """Classify the intent of a user query."""
        
        prompt = f"""You are an intent classifier for a chatbot that can handle two types of queries:

1. RAG (Retrieval-Augmented Generation): For questions about documents, policies, procedures, general information, explanations, how-to guides, company information, product manuals, research papers, etc.

2. SQL (Statistical/Database): For questions requiring data analysis, statistics, calculations, trends, comparisons, counts, sums, averages, financial metrics, sales data, employee data, performance metrics, etc.

Examples of RAG queries:
- "What is the company's vacation policy?"
- "How do I set up the SmartHome device?"
- "What are the latest advances in AI research?"
- "Tell me about the company benefits"
- "What is the code of conduct?"

Examples of SQL queries:
- "What is the average salary by department?"
- "How many sales were made last quarter?"
- "Show me the top performing employees"
- "What is the total revenue for 2024?"
- "Which region has the highest sales?"
- "What is the customer satisfaction score?"

Analyze the following query and determine which type it is. Respond with ONLY one word: either "RAG" or "SQL".

Query: {query}

Classification:"""
        
        try:
            response = self.llm.invoke(prompt)
            classification = response.content.strip().upper()
            
            # Validate response
            if classification not in ["RAG", "SQL"]:
                # Default to RAG for ambiguous cases
                classification = "RAG"
            
            # Calculate confidence based on keywords
            confidence = self._calculate_confidence(query, classification)
            
            return {
                "intent": classification,
                "confidence": confidence,
                "reasoning": self._get_reasoning(query, classification)
            }
            
        except Exception as e:
            # Default to RAG in case of error
            return {
                "intent": "RAG",
                "confidence": 0.5,
                "reasoning": f"Error in classification: {e}. Defaulting to RAG."
            }
    
    def _calculate_confidence(self, query: str, classification: str) -> float:
        """Calculate confidence score based on keyword analysis."""
        query_lower = query.lower()
        
        # SQL keywords
        sql_keywords = [
            'average', 'mean', 'sum', 'total', 'count', 'how many', 'statistics',
            'revenue', 'sales', 'profit', 'salary', 'performance', 'score',
            'top', 'bottom', 'highest', 'lowest', 'compare', 'comparison',
            'trend', 'growth', 'percentage', 'ratio', 'metric', 'data',
            'employee', 'customer', 'financial', 'quarter', 'year', 'month',
            'department', 'region', 'category', 'analysis', 'report'
        ]
        
        # RAG keywords
        rag_keywords = [
            'what is', 'how to', 'explain', 'tell me about', 'describe',
            'policy', 'procedure', 'guide', 'manual', 'handbook',
            'benefits', 'rules', 'conduct', 'overview', 'information',
            'setup', 'install', 'configure', 'research', 'study',
            'definition', 'meaning', 'purpose', 'background'
        ]
        
        sql_score = sum(1 for keyword in sql_keywords if keyword in query_lower)
        rag_score = sum(1 for keyword in rag_keywords if keyword in query_lower)
        
        if classification == "SQL":
            if sql_score > rag_score:
                return min(0.9, 0.6 + (sql_score - rag_score) * 0.1)
            else:
                return 0.6
        else:  # RAG
            if rag_score > sql_score:
                return min(0.9, 0.6 + (rag_score - sql_score) * 0.1)
            else:
                return 0.6
    
    def _get_reasoning(self, query: str, classification: str) -> str:
        """Provide reasoning for the classification."""
        query_lower = query.lower()
        
        if classification == "SQL":
            sql_indicators = []
            if any(word in query_lower for word in ['average', 'mean', 'sum', 'total']):
                sql_indicators.append("contains aggregation keywords")
            if any(word in query_lower for word in ['how many', 'count']):
                sql_indicators.append("asks for counting/quantification")
            if any(word in query_lower for word in ['revenue', 'sales', 'salary', 'profit']):
                sql_indicators.append("references financial/numerical data")
            if any(word in query_lower for word in ['top', 'highest', 'lowest', 'compare']):
                sql_indicators.append("requires ranking/comparison")
            
            if sql_indicators:
                return f"Classified as SQL because query {', '.join(sql_indicators)}"
            else:
                return "Classified as SQL based on overall context suggesting data analysis"
        
        else:  # RAG
            rag_indicators = []
            if any(word in query_lower for word in ['what is', 'how to', 'explain']):
                rag_indicators.append("asks for explanation/information")
            if any(word in query_lower for word in ['policy', 'procedure', 'guide', 'manual']):
                rag_indicators.append("references documentation")
            if any(word in query_lower for word in ['tell me about', 'describe']):
                rag_indicators.append("requests descriptive information")
            
            if rag_indicators:
                return f"Classified as RAG because query {', '.join(rag_indicators)}"
            else:
                return "Classified as RAG as default for general information queries"

# Initialize intent classifier (will be imported by other modules)
intent_classifier = None

def get_intent_classifier():
    """Get or create intent classifier instance."""
    global intent_classifier
    if intent_classifier is None:
        intent_classifier = IntentClassifier()
    return intent_classifier

def classify_query(query: str) -> Dict[str, Any]:
    """Convenience function to classify a query."""
    classifier = get_intent_classifier()
    return classifier.classify_intent(query)
