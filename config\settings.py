"""Configuration settings for the agentic chatbot."""

import os
from pathlib import Path
from pydantic_settings import BaseSettings
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

class Settings(BaseSettings):
    """Application settings."""
    
    # API Keys
    openai_api_key: str = os.getenv("OPENAI_API_KEY", "")
    
    # Database
    database_url: str = os.getenv("DATABASE_URL", "sqlite:///./chatbot.db")
    
    # Vector Database
    chroma_persist_directory: str = os.getenv("CHROMA_PERSIST_DIRECTORY", "./chroma_db")
    
    # Application
    app_name: str = os.getenv("APP_NAME", "Agentic Chatbot")
    debug: bool = os.getenv("DEBUG", "True").lower() == "true"
    
    # Paths
    documents_path: Path = Path("documents")
    data_path: Path = Path("data")
    database_path: Path = Path("database")
    
    # Model Configuration
    embedding_model: str = "text-embedding-ada-002"
    chat_model: str = "gpt-3.5-turbo"
    temperature: float = 0.7
    max_tokens: int = 1000
    
    # RAG Configuration
    chunk_size: int = 1000
    chunk_overlap: int = 200
    max_retrieved_docs: int = 5
    
    class Config:
        env_file = ".env"

# Global settings instance
settings = Settings()
