"""Test complete agent routing with both SQL and RAG."""

def test_complete_routing():
    """Test the complete agent routing system."""
    from agents.main_agent import get_main_agent
    
    agent = get_main_agent()
    
    print("🤖 Testing Complete Agent Routing")
    print("=" * 50)
    
    # Test SQL queries
    print("\n📊 Testing SQL Queries:")
    sql_queries = [
        "How many employees are there?",
        "What is the average salary?",
        "What is the total revenue?"
    ]
    
    for query in sql_queries:
        print(f"\n🔍 Query: {query}")
        result = agent.process_query(query)
        print(f"🤖 Agent Used: {result['agent_used']}")
        print(f"✅ Answer: {result['answer'][:100]}...")
        print(f"🎯 Intent: {result['intent']}")
        print(f"✅ Success: {result.get('success', 'N/A')}")
    
    # Test RAG queries
    print("\n📚 Testing RAG Queries:")
    rag_queries = [
        "What is the company's vacation policy?",
        "How do I set up the SmartHome device?",
        "Tell me about the company benefits"
    ]
    
    for query in rag_queries:
        print(f"\n🔍 Query: {query}")
        result = agent.process_query(query)
        print(f"🤖 Agent Used: {result['agent_used']}")
        print(f"✅ Answer: {result['answer'][:100]}...")
        print(f"🎯 Intent: {result['intent']}")
        print(f"📚 Sources: {result.get('sources', [])}")

if __name__ == "__main__":
    test_complete_routing()
