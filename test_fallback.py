"""Test the fallback system without OpenAI API calls."""

def test_fallback_rag():
    """Test the fallback RAG agent."""
    print("🧪 Testing Fallback RAG Agent...")
    
    try:
        from agents.fallback_rag_agent import get_fallback_rag_agent
        
        agent = get_fallback_rag_agent()
        
        # Test queries
        test_queries = [
            "What is the company vacation policy?",
            "How do I set up the SmartHome device?",
            "What are the benefits?",
            "Tell me about AI research"
        ]
        
        for query in test_queries:
            print(f"\n📝 Query: {query}")
            result = agent.query(query)
            print(f"✅ Answer: {result['answer'][:100]}...")
            print(f"📚 Sources: {result['sources']}")
            print(f"🎯 Confidence: {result['confidence']:.2f}")
        
        return True
        
    except Exception as e:
        print(f"❌ Fallback RAG test failed: {e}")
        return False

def test_main_agent_fallback():
    """Test the main agent with fallback functionality."""
    print("\n🧪 Testing Main Agent with Fallback...")
    
    try:
        from agents.main_agent import get_main_agent
        
        agent = get_main_agent()
        
        # Test both types of queries
        test_queries = [
            ("What is the company vacation policy?", "RAG"),
            ("How many employees are there?", "SQL"),
            ("What are the benefits?", "RAG"),
            ("What is the average salary?", "SQL")
        ]
        
        for query, expected_type in test_queries:
            print(f"\n📝 Query: {query}")
            result = agent.process_query(query)
            print(f"🤖 Agent: {result['agent_used']}")
            print(f"✅ Answer: {result['answer'][:100]}...")
            print(f"🎯 Intent: {result['intent']}")
        
        return True
        
    except Exception as e:
        print(f"❌ Main agent fallback test failed: {e}")
        return False

def main():
    """Run fallback tests."""
    print("🔄 Testing Fallback System (No OpenAI Required)")
    print("=" * 50)
    
    tests = [
        ("Fallback RAG", test_fallback_rag),
        ("Main Agent Fallback", test_main_agent_fallback)
    ]
    
    results = {}
    
    for test_name, test_func in tests:
        print(f"\n{'='*20} {test_name} {'='*20}")
        results[test_name] = test_func()
    
    # Summary
    print("\n" + "=" * 50)
    print("📊 FALLBACK TEST SUMMARY")
    print("=" * 50)
    
    passed = sum(results.values())
    total = len(results)
    
    for test_name, result in results.items():
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{test_name:20} {status}")
    
    print(f"\nOverall: {passed}/{total} tests passed")
    
    if passed == total:
        print("\n🎉 Fallback system is working!")
        print("💡 Your chatbot can now work even without OpenAI API!")
    else:
        print("\n⚠️ Some fallback tests failed.")

if __name__ == "__main__":
    main()
