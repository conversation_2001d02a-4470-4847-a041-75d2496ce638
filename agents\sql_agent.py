"""SQL Agent for handling structured data queries."""

import sqlite3
import pandas as pd
from typing import Dict, Any, List
from langchain_openai import Chat<PERSON>penA<PERSON>
from config.settings import settings

class SQLAgent:
    """Agent for handling SQL queries and statistical analysis."""
    
    def __init__(self, db_path: str = "database/chatbot.db"):
        """Initialize the SQL agent."""
        self.db_path = db_path
        self.llm = ChatOpenAI(
            openai_api_key=settings.openai_api_key,
            model_name=settings.chat_model,
            temperature=0.1,  # Lower temperature for more precise SQL generation
            max_tokens=settings.max_tokens
        )
        
        # Get database schema
        self.schema = self._get_database_schema()
    
    def _get_database_schema(self) -> str:
        """Get the database schema information."""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            # Get all table names
            cursor.execute("SELECT name FROM sqlite_master WHERE type='table';")
            tables = cursor.fetchall()
            
            schema_info = "Database Schema:\n\n"
            
            for table in tables:
                table_name = table[0]
                schema_info += f"Table: {table_name}\n"
                
                # Get column information
                cursor.execute(f"PRAGMA table_info({table_name});")
                columns = cursor.fetchall()
                
                for col in columns:
                    col_name, col_type = col[1], col[2]
                    schema_info += f"  - {col_name}: {col_type}\n"
                
                # Get sample data
                cursor.execute(f"SELECT * FROM {table_name} LIMIT 3;")
                sample_data = cursor.fetchall()
                
                if sample_data:
                    schema_info += f"  Sample data:\n"
                    for row in sample_data:
                        schema_info += f"    {row}\n"
                
                schema_info += "\n"
            
            conn.close()
            return schema_info
            
        except Exception as e:
            return f"Error getting schema: {e}"
    
    def generate_sql_query(self, question: str) -> str:
        """Generate SQL query from natural language question."""
        prompt = f"""You are a SQL expert. Given the following database schema and a user question, 
generate a precise SQL query to answer the question.

{self.schema}

Rules:
1. Only use tables and columns that exist in the schema
2. Use proper SQL syntax for SQLite
3. For statistical questions, use appropriate aggregate functions (COUNT, SUM, AVG, MAX, MIN)
4. Use GROUP BY when needed for categorical analysis
5. Use ORDER BY to sort results meaningfully
6. Limit results to reasonable numbers (use LIMIT when appropriate)
7. Use proper date functions for date-based queries
8. Return only the SQL query, no explanations

User question: {question}

SQL Query:"""
        
        try:
            response = self.llm.invoke(prompt)
            sql_query = response.content.strip()
            
            # Clean up the response (remove markdown formatting if present)
            if sql_query.startswith("```sql"):
                sql_query = sql_query[6:]
            if sql_query.endswith("```"):
                sql_query = sql_query[:-3]
            
            return sql_query.strip()
            
        except Exception as e:
            return f"Error generating SQL: {e}"
    
    def execute_query(self, sql_query: str) -> Dict[str, Any]:
        """Execute SQL query and return results."""
        try:
            conn = sqlite3.connect(self.db_path)
            
            # Execute query and get results as DataFrame
            df = pd.read_sql_query(sql_query, conn)
            conn.close()
            
            # Convert DataFrame to dictionary format
            if df.empty:
                return {
                    "success": True,
                    "data": [],
                    "columns": [],
                    "row_count": 0
                }
            
            return {
                "success": True,
                "data": df.to_dict('records'),
                "columns": df.columns.tolist(),
                "row_count": len(df),
                "summary_stats": self._generate_summary_stats(df)
            }
            
        except Exception as e:
            return {
                "success": False,
                "error": str(e),
                "data": [],
                "columns": [],
                "row_count": 0
            }
    
    def _generate_summary_stats(self, df: pd.DataFrame) -> Dict[str, Any]:
        """Generate summary statistics for numeric columns."""
        summary = {}
        
        numeric_columns = df.select_dtypes(include=['number']).columns
        
        for col in numeric_columns:
            summary[col] = {
                "mean": round(df[col].mean(), 2) if not df[col].isna().all() else None,
                "median": round(df[col].median(), 2) if not df[col].isna().all() else None,
                "std": round(df[col].std(), 2) if not df[col].isna().all() else None,
                "min": df[col].min(),
                "max": df[col].max(),
                "count": df[col].count()
            }
        
        return summary
    
    def format_results(self, results: Dict[str, Any], original_question: str) -> str:
        """Format query results into a human-readable response."""
        if not results["success"]:
            return f"I encountered an error while querying the database: {results['error']}"
        
        if results["row_count"] == 0:
            return "No data found matching your query criteria."
        
        # Generate natural language response
        prompt = f"""Based on the following SQL query results, provide a clear and comprehensive answer 
to the user's question. Include key insights, trends, and specific numbers from the data.

Original question: {original_question}

Query results:
- Number of rows: {results['row_count']}
- Columns: {results['columns']}
- Data: {results['data'][:10]}  # Show first 10 rows

Summary statistics (if available):
{results.get('summary_stats', {})}

Please provide a natural language response that:
1. Directly answers the user's question
2. Highlights key findings and insights
3. Includes specific numbers and percentages where relevant
4. Is easy to understand for non-technical users

Response:"""
        
        try:
            response = self.llm.invoke(prompt)
            return response.content
        except Exception as e:
            # Fallback to basic formatting
            return self._basic_format_results(results)
    
    def _basic_format_results(self, results: Dict[str, Any]) -> str:
        """Basic formatting of results as fallback."""
        if results["row_count"] == 0:
            return "No results found."
        
        response = f"Found {results['row_count']} result(s):\n\n"
        
        # Show first few rows
        for i, row in enumerate(results["data"][:5]):
            response += f"Row {i+1}:\n"
            for col, value in row.items():
                response += f"  {col}: {value}\n"
            response += "\n"
        
        if results["row_count"] > 5:
            response += f"... and {results['row_count'] - 5} more rows\n"
        
        return response
    
    def query(self, question: str) -> Dict[str, Any]:
        """Main method to handle SQL queries."""
        print(f"SQL Agent processing query: {question}")
        
        # Generate SQL query
        sql_query = self.generate_sql_query(question)
        
        if sql_query.startswith("Error"):
            return {
                "answer": f"I couldn't generate a proper SQL query for your question: {sql_query}",
                "sql_query": None,
                "success": False
            }
        
        # Execute query
        results = self.execute_query(sql_query)
        
        # Format response
        if results["success"]:
            answer = self.format_results(results, question)
            return {
                "answer": answer,
                "sql_query": sql_query,
                "data": results["data"],
                "row_count": results["row_count"],
                "success": True
            }
        else:
            return {
                "answer": f"Error executing query: {results['error']}",
                "sql_query": sql_query,
                "success": False
            }

# Initialize SQL agent (will be imported by other modules)
sql_agent = None

def get_sql_agent():
    """Get or create SQL agent instance."""
    global sql_agent
    if sql_agent is None:
        sql_agent = SQLAgent()
    return sql_agent
