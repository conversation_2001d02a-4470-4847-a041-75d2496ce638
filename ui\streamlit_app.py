"""Streamlit UI for the Agentic Chatbot."""

import streamlit as st
import sys
import os
from datetime import datetime
import json

# Add parent directory to path to import agents
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from agents.main_agent import get_main_agent
from config.settings import settings

# Page configuration
st.set_page_config(
    page_title="Agentic Chatbot",
    page_icon="🤖",
    layout="wide",
    initial_sidebar_state="expanded"
)

# Custom CSS
st.markdown("""
<style>
    .main-header {
        font-size: 2.5rem;
        font-weight: bold;
        color: #1f77b4;
        text-align: center;
        margin-bottom: 2rem;
    }
    .agent-badge {
        display: inline-block;
        padding: 0.25rem 0.5rem;
        border-radius: 0.25rem;
        font-size: 0.75rem;
        font-weight: bold;
        margin-bottom: 0.5rem;
    }
    .rag-badge {
        background-color: #e8f4fd;
        color: #1f77b4;
    }
    .sql-badge {
        background-color: #fff2e8;
        color: #ff7f0e;
    }
    .confidence-high {
        color: #2ca02c;
    }
    .confidence-medium {
        color: #ff7f0e;
    }
    .confidence-low {
        color: #d62728;
    }
</style>
""", unsafe_allow_html=True)

def initialize_session_state():
    """Initialize session state variables."""
    if "messages" not in st.session_state:
        st.session_state.messages = []
    if "main_agent" not in st.session_state:
        st.session_state.main_agent = get_main_agent()
    if "rag_initialized" not in st.session_state:
        st.session_state.rag_initialized = False

def initialize_rag_system():
    """Initialize the RAG system."""
    if not st.session_state.rag_initialized:
        with st.spinner("Initializing RAG system (indexing documents)..."):
            result = st.session_state.main_agent.initialize_rag_system()
            if result["success"]:
                st.session_state.rag_initialized = True
                st.success("RAG system initialized successfully!")
            else:
                st.error(f"Failed to initialize RAG system: {result['message']}")

def display_message(message, is_user=True):
    """Display a chat message."""
    if is_user:
        with st.chat_message("user"):
            st.write(message)
    else:
        with st.chat_message("assistant"):
            st.write(message)

def format_response(response):
    """Format the agent response for display."""
    answer = response.get("answer", "No answer provided")
    agent_used = response.get("agent_used", "Unknown")
    confidence = response.get("confidence", 0.0)
    
    # Agent badge
    if agent_used == "RAG":
        badge_class = "rag-badge"
    elif agent_used == "SQL":
        badge_class = "sql-badge"
    else:
        badge_class = "agent-badge"
    
    # Confidence color
    if confidence > 0.8:
        conf_class = "confidence-high"
    elif confidence > 0.5:
        conf_class = "confidence-medium"
    else:
        conf_class = "confidence-low"
    
    # Format main response
    formatted_response = f"""
    <div class="agent-badge {badge_class}">
        {agent_used} Agent
    </div>
    
    {answer}
    """
    
    # Add additional information based on agent type
    if agent_used == "RAG":
        sources = response.get("sources", [])
        if sources:
            formatted_response += f"\n\n**Sources:** {', '.join(sources)}"
        
        retrieved_chunks = response.get("retrieved_chunks", 0)
        if retrieved_chunks > 0:
            formatted_response += f"\n\n*Retrieved {retrieved_chunks} relevant document chunks*"
    
    elif agent_used == "SQL":
        sql_query = response.get("sql_query")
        if sql_query:
            formatted_response += f"\n\n**SQL Query:**\n```sql\n{sql_query}\n```"
        
        row_count = response.get("row_count", 0)
        if row_count > 0:
            formatted_response += f"\n\n*Query returned {row_count} rows*"
    
    # Add confidence and reasoning
    reasoning = response.get("reasoning", "")
    if reasoning:
        formatted_response += f"\n\n<small><span class='{conf_class}'>Confidence: {confidence:.2f}</span> | {reasoning}</small>"
    
    return formatted_response

def display_system_status():
    """Display system status in sidebar."""
    st.sidebar.header("System Status")
    
    try:
        status = st.session_state.main_agent.get_system_status()
        
        # Overall status
        st.sidebar.success(f"Status: {status['status'].title()}")
        
        # Agent status
        st.sidebar.subheader("Agents")
        
        rag_status = status["agents"]["rag"]
        if rag_status["status"] == "ready":
            st.sidebar.success(f"RAG: Ready ({rag_status.get('document_count', 0)} docs)")
        else:
            st.sidebar.warning(f"RAG: {rag_status['status']}")
        
        sql_status = status["agents"]["sql"]
        if sql_status["status"] == "ready":
            st.sidebar.success(f"SQL: Ready ({sql_status.get('table_count', 0)} tables)")
        else:
            st.sidebar.warning(f"SQL: {sql_status['status']}")
        
        # Conversation stats
        st.sidebar.subheader("Session Stats")
        st.sidebar.info(f"Messages: {len(st.session_state.messages)}")
        st.sidebar.info(f"Total History: {status['conversation_history_count']}")
        
    except Exception as e:
        st.sidebar.error(f"Error getting status: {e}")

def display_sample_queries():
    """Display sample queries for users to try."""
    st.sidebar.header("Sample Queries")
    
    st.sidebar.subheader("📊 SQL/Statistical Queries")
    sql_samples = [
        "What is the average salary by department?",
        "How many sales were made in Q4 2024?",
        "Which region has the highest total sales?",
        "What is the customer satisfaction score for Electronics?",
        "Show me the top 5 performing employees"
    ]
    
    for query in sql_samples:
        if st.sidebar.button(query, key=f"sql_{hash(query)}"):
            st.session_state.sample_query = query
    
    st.sidebar.subheader("📚 RAG/Document Queries")
    rag_samples = [
        "What is the company's vacation policy?",
        "How do I set up the SmartHome AI Assistant?",
        "What are the latest advances in large language models?",
        "Tell me about the company benefits package",
        "What is the code of conduct?"
    ]
    
    for query in rag_samples:
        if st.sidebar.button(query, key=f"rag_{hash(query)}"):
            st.session_state.sample_query = query

def main():
    """Main Streamlit application."""
    initialize_session_state()
    
    # Header
    st.markdown('<h1 class="main-header">🤖 Agentic Chatbot</h1>', unsafe_allow_html=True)
    st.markdown("---")
    
    # Initialize RAG system if needed
    if not st.session_state.rag_initialized:
        st.info("Welcome! The system needs to index documents for the RAG functionality.")
        if st.button("Initialize RAG System"):
            initialize_rag_system()
    
    # Sidebar
    display_system_status()
    st.sidebar.markdown("---")
    display_sample_queries()
    
    # Main chat interface
    col1, col2 = st.columns([3, 1])
    
    with col1:
        # Display chat messages
        for message in st.session_state.messages:
            if message["role"] == "user":
                display_message(message["content"], is_user=True)
            else:
                with st.chat_message("assistant"):
                    st.markdown(message["content"], unsafe_allow_html=True)
        
        # Chat input
        query = st.chat_input("Ask me anything about documents or data...")
        
        # Handle sample query selection
        if "sample_query" in st.session_state:
            query = st.session_state.sample_query
            del st.session_state.sample_query
        
        if query:
            # Add user message
            st.session_state.messages.append({"role": "user", "content": query})
            display_message(query, is_user=True)
            
            # Get response from agent
            with st.chat_message("assistant"):
                with st.spinner("Processing your query..."):
                    response = st.session_state.main_agent.process_query(query)
                
                formatted_response = format_response(response)
                st.markdown(formatted_response, unsafe_allow_html=True)
                
                # Add assistant message
                st.session_state.messages.append({
                    "role": "assistant", 
                    "content": formatted_response
                })
    
    with col2:
        st.subheader("Query Details")
        
        if st.session_state.messages:
            # Show details of last response
            if len(st.session_state.messages) >= 2:
                last_query = st.session_state.messages[-2]["content"]
                
                # Get the last response details (this is a simplified approach)
                st.write("**Last Query:**")
                st.write(last_query)
                
                # You could store more detailed response info in session state
                # and display it here (SQL queries, sources, etc.)
        
        # Clear chat button
        if st.button("Clear Chat"):
            st.session_state.messages = []
            st.rerun()

if __name__ == "__main__":
    main()
