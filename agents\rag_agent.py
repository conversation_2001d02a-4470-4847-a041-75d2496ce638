"""RAG Agent for handling unstructured document queries."""

import os
from typing import List, Dict, Any
from pathlib import Path
import <PERSON>yPDF2
import chromadb
from chromadb.config import Settings
from langchain.text_splitter import RecursiveCharacterTextSplitter
from langchain_openai import OpenAIEmbeddings, ChatOpenAI
from langchain.schema import Document
from config.settings import settings

class RAGAgent:
    """Agent for Retrieval-Augmented Generation from documents."""
    
    def __init__(self):
        """Initialize the RAG agent."""
        self.embeddings = OpenAIEmbeddings(
            openai_api_key=settings.openai_api_key,
            model=settings.embedding_model
        )
        
        self.llm = ChatOpenAI(
            openai_api_key=settings.openai_api_key,
            model_name=settings.chat_model,
            temperature=settings.temperature,
            max_tokens=settings.max_tokens
        )
        
        self.text_splitter = RecursiveCharacterTextSplitter(
            chunk_size=settings.chunk_size,
            chunk_overlap=settings.chunk_overlap,
            length_function=len,
        )
        
        # Initialize ChromaDB
        self.chroma_client = chromadb.PersistentClient(
            path=settings.chroma_persist_directory
        )
        
        self.collection_name = "documents"
        self.collection = None
        self._initialize_collection()
    
    def _initialize_collection(self):
        """Initialize or get the ChromaDB collection."""
        try:
            self.collection = self.chroma_client.get_collection(self.collection_name)
            print(f"Loaded existing collection: {self.collection_name}")
        except Exception:
            self.collection = self.chroma_client.create_collection(
                name=self.collection_name,
                metadata={"description": "Document collection for RAG"}
            )
            print(f"Created new collection: {self.collection_name}")
    
    def extract_text_from_pdf(self, pdf_path: str) -> str:
        """Extract text from a PDF file."""
        text = ""
        try:
            with open(pdf_path, 'rb') as file:
                pdf_reader = PyPDF2.PdfReader(file)
                for page in pdf_reader.pages:
                    text += page.extract_text() + "\n"
        except Exception as e:
            print(f"Error extracting text from {pdf_path}: {e}")
        return text
    
    def load_documents(self, documents_dir: str = "documents") -> List[Document]:
        """Load and process documents from the documents directory."""
        documents = []
        documents_path = Path(documents_dir)
        
        if not documents_path.exists():
            print(f"Documents directory {documents_dir} does not exist")
            return documents
        
        for file_path in documents_path.glob("*.pdf"):
            print(f"Processing: {file_path}")
            text = self.extract_text_from_pdf(str(file_path))
            
            if text.strip():
                doc = Document(
                    page_content=text,
                    metadata={
                        "source": str(file_path),
                        "filename": file_path.name,
                        "type": "pdf"
                    }
                )
                documents.append(doc)
        
        return documents
    
    def chunk_documents(self, documents: List[Document]) -> List[Document]:
        """Split documents into chunks."""
        chunks = []
        for doc in documents:
            doc_chunks = self.text_splitter.split_documents([doc])
            for i, chunk in enumerate(doc_chunks):
                chunk.metadata.update({
                    "chunk_id": i,
                    "total_chunks": len(doc_chunks)
                })
            chunks.extend(doc_chunks)
        return chunks
    
    def index_documents(self, force_reindex: bool = False):
        """Index documents in the vector database."""
        # Check if collection already has documents
        if not force_reindex:
            try:
                count = self.collection.count()
                if count > 0:
                    print(f"Collection already contains {count} documents. Use force_reindex=True to reindex.")
                    return
            except Exception:
                pass
        
        # Load and process documents
        print("Loading documents...")
        documents = self.load_documents()
        
        if not documents:
            print("No documents found to index")
            return
        
        print(f"Loaded {len(documents)} documents")
        
        # Chunk documents
        print("Chunking documents...")
        chunks = self.chunk_documents(documents)
        print(f"Created {len(chunks)} chunks")
        
        # Clear existing collection if reindexing
        if force_reindex:
            self.chroma_client.delete_collection(self.collection_name)
            self.collection = self.chroma_client.create_collection(
                name=self.collection_name,
                metadata={"description": "Document collection for RAG"}
            )
        
        # Generate embeddings and add to collection
        print("Generating embeddings and indexing...")
        for i, chunk in enumerate(chunks):
            try:
                # Generate embedding
                embedding = self.embeddings.embed_query(chunk.page_content)
                
                # Add to collection
                self.collection.add(
                    embeddings=[embedding],
                    documents=[chunk.page_content],
                    metadatas=[chunk.metadata],
                    ids=[f"chunk_{i}"]
                )
                
                if (i + 1) % 10 == 0:
                    print(f"Indexed {i + 1}/{len(chunks)} chunks")
            
            except Exception as e:
                print(f"Error indexing chunk {i}: {e}")
        
        print(f"Successfully indexed {len(chunks)} document chunks")
    
    def retrieve_documents(self, query: str, n_results: int = None) -> List[Dict[str, Any]]:
        """Retrieve relevant documents for a query."""
        if n_results is None:
            n_results = settings.max_retrieved_docs
        
        try:
            # Generate query embedding
            query_embedding = self.embeddings.embed_query(query)
            
            # Search in collection
            results = self.collection.query(
                query_embeddings=[query_embedding],
                n_results=n_results
            )
            
            # Format results
            retrieved_docs = []
            if results['documents'] and results['documents'][0]:
                for i, doc in enumerate(results['documents'][0]):
                    retrieved_docs.append({
                        'content': doc,
                        'metadata': results['metadatas'][0][i] if results['metadatas'] else {},
                        'distance': results['distances'][0][i] if results['distances'] else 0
                    })
            
            return retrieved_docs
        
        except Exception as e:
            print(f"Error retrieving documents: {e}")
            return []
    
    def generate_response(self, query: str, retrieved_docs: List[Dict[str, Any]]) -> str:
        """Generate response using retrieved documents."""
        if not retrieved_docs:
            return "I couldn't find any relevant information in the documents to answer your question."
        
        # Prepare context from retrieved documents
        context = "\n\n".join([
            f"Document: {doc['metadata'].get('filename', 'Unknown')}\n{doc['content']}"
            for doc in retrieved_docs
        ])
        
        # Create prompt
        prompt = f"""Based on the following documents, please answer the user's question. 
If the information is not available in the documents, please say so.

Context from documents:
{context}

User question: {query}

Please provide a comprehensive answer based on the information in the documents:"""
        
        try:
            response = self.llm.invoke(prompt)
            return response.content
        except Exception as e:
            return f"Error generating response: {e}"
    
    def query(self, question: str) -> Dict[str, Any]:
        """Main method to handle RAG queries."""
        print(f"RAG Agent processing query: {question}")
        
        # Retrieve relevant documents
        retrieved_docs = self.retrieve_documents(question)
        
        if not retrieved_docs:
            return {
                "answer": "I couldn't find any relevant information in the documents to answer your question.",
                "sources": [],
                "confidence": 0.0
            }
        
        # Generate response
        answer = self.generate_response(question, retrieved_docs)
        
        # Extract sources
        sources = list(set([
            doc['metadata'].get('filename', 'Unknown')
            for doc in retrieved_docs
        ]))
        
        return {
            "answer": answer,
            "sources": sources,
            "confidence": 1.0 - min([doc['distance'] for doc in retrieved_docs]),
            "retrieved_chunks": len(retrieved_docs)
        }

# Initialize RAG agent (will be imported by other modules)
rag_agent = None

def get_rag_agent():
    """Get or create RAG agent instance."""
    global rag_agent
    if rag_agent is None:
        rag_agent = RAGAgent()
    return rag_agent
