"""Script to create sample SQLite database with statistical data."""

import sqlite3
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import random

def create_database():
    """Create SQLite database with sample tables."""
    conn = sqlite3.connect('database/chatbot.db')
    cursor = conn.cursor()
    
    # Create employees table
    cursor.execute('''
    CREATE TABLE IF NOT EXISTS employees (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        name TEXT NOT NULL,
        department TEXT NOT NULL,
        position TEXT NOT NULL,
        salary REAL NOT NULL,
        hire_date DATE NOT NULL,
        age INTEGER NOT NULL,
        performance_score REAL NOT NULL
    )
    ''')
    
    # Create sales table
    cursor.execute('''
    CREATE TABLE IF NOT EXISTS sales (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        product_name TEXT NOT NULL,
        category TEXT NOT NULL,
        sale_date DATE NOT NULL,
        quantity INTEGER NOT NULL,
        unit_price REAL NOT NULL,
        total_amount REAL NOT NULL,
        region TEXT NOT NULL,
        salesperson_id INTEGER,
        <PERSON>OR<PERSON><PERSON><PERSON> KEY (salesperson_id) REFERENCES employees (id)
    )
    ''')
    
    # Create customer_satisfaction table
    cursor.execute('''
    CREATE TABLE IF NOT EXISTS customer_satisfaction (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        customer_id INTEGER NOT NULL,
        survey_date DATE NOT NULL,
        overall_rating INTEGER NOT NULL,
        product_quality INTEGER NOT NULL,
        customer_service INTEGER NOT NULL,
        delivery_speed INTEGER NOT NULL,
        would_recommend INTEGER NOT NULL,
        product_category TEXT NOT NULL
    )
    ''')
    
    # Create financial_metrics table
    cursor.execute('''
    CREATE TABLE IF NOT EXISTS financial_metrics (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        metric_date DATE NOT NULL,
        revenue REAL NOT NULL,
        expenses REAL NOT NULL,
        profit REAL NOT NULL,
        quarter TEXT NOT NULL,
        year INTEGER NOT NULL
    )
    ''')
    
    conn.commit()
    return conn

def populate_employees(conn):
    """Populate employees table with sample data."""
    departments = ['Engineering', 'Sales', 'Marketing', 'HR', 'Finance', 'Operations']
    positions = {
        'Engineering': ['Software Engineer', 'Senior Engineer', 'Tech Lead', 'Engineering Manager'],
        'Sales': ['Sales Rep', 'Senior Sales Rep', 'Sales Manager', 'VP Sales'],
        'Marketing': ['Marketing Specialist', 'Marketing Manager', 'Content Creator', 'CMO'],
        'HR': ['HR Specialist', 'HR Manager', 'Recruiter', 'HR Director'],
        'Finance': ['Financial Analyst', 'Accountant', 'Finance Manager', 'CFO'],
        'Operations': ['Operations Specialist', 'Operations Manager', 'VP Operations']
    }
    
    employees_data = []
    names = ['John Smith', 'Jane Doe', 'Mike Johnson', 'Sarah Wilson', 'David Brown', 
             'Lisa Davis', 'Chris Miller', 'Amanda Garcia', 'Robert Taylor', 'Emily Anderson',
             'James Martinez', 'Jessica Thompson', 'Daniel White', 'Ashley Harris', 'Matthew Clark',
             'Stephanie Lewis', 'Kevin Robinson', 'Nicole Walker', 'Brian Hall', 'Megan Young']
    
    for i, name in enumerate(names):
        dept = random.choice(departments)
        position = random.choice(positions[dept])
        
        # Salary based on position level
        base_salary = {
            'Specialist': 60000, 'Rep': 55000, 'Engineer': 80000, 'Analyst': 65000,
            'Creator': 58000, 'Recruiter': 62000, 'Accountant': 68000,
            'Senior': 90000, 'Manager': 110000, 'Lead': 120000,
            'Director': 140000, 'VP': 160000, 'CMO': 180000, 'CFO': 200000
        }
        
        salary = 50000
        for key, value in base_salary.items():
            if key in position:
                salary = value + random.randint(-10000, 15000)
                break
        
        hire_date = datetime.now() - timedelta(days=random.randint(30, 2000))
        age = random.randint(25, 60)
        performance_score = round(random.uniform(3.0, 5.0), 1)
        
        employees_data.append((name, dept, position, salary, hire_date.date(), age, performance_score))
    
    cursor = conn.cursor()
    cursor.executemany('''
        INSERT INTO employees (name, department, position, salary, hire_date, age, performance_score)
        VALUES (?, ?, ?, ?, ?, ?, ?)
    ''', employees_data)
    conn.commit()
    print(f"Inserted {len(employees_data)} employees")

def populate_sales(conn):
    """Populate sales table with sample data."""
    products = {
        'Electronics': ['Laptop Pro', 'Smartphone X', 'Tablet Plus', 'Smart Watch', 'Headphones'],
        'Software': ['AI Suite', 'Analytics Pro', 'Security Shield', 'Cloud Storage', 'Productivity Pack'],
        'Services': ['Consulting', 'Training', 'Support', 'Implementation', 'Maintenance']
    }
    
    regions = ['North', 'South', 'East', 'West', 'Central']
    
    sales_data = []
    
    # Generate sales for the last 2 years
    start_date = datetime.now() - timedelta(days=730)
    
    for _ in range(1000):  # Generate 1000 sales records
        category = random.choice(list(products.keys()))
        product = random.choice(products[category])
        
        sale_date = start_date + timedelta(days=random.randint(0, 730))
        quantity = random.randint(1, 50)
        
        # Price based on category
        price_ranges = {
            'Electronics': (500, 2000),
            'Software': (100, 1000),
            'Services': (200, 5000)
        }
        
        unit_price = random.uniform(*price_ranges[category])
        total_amount = quantity * unit_price
        region = random.choice(regions)
        salesperson_id = random.randint(1, 20)
        
        sales_data.append((product, category, sale_date.date(), quantity, 
                          round(unit_price, 2), round(total_amount, 2), region, salesperson_id))
    
    cursor = conn.cursor()
    cursor.executemany('''
        INSERT INTO sales (product_name, category, sale_date, quantity, unit_price, total_amount, region, salesperson_id)
        VALUES (?, ?, ?, ?, ?, ?, ?, ?)
    ''', sales_data)
    conn.commit()
    print(f"Inserted {len(sales_data)} sales records")

def populate_customer_satisfaction(conn):
    """Populate customer satisfaction table."""
    categories = ['Electronics', 'Software', 'Services']
    
    satisfaction_data = []
    start_date = datetime.now() - timedelta(days=365)
    
    for _ in range(500):  # Generate 500 survey responses
        customer_id = random.randint(1, 200)
        survey_date = start_date + timedelta(days=random.randint(0, 365))
        
        # Ratings from 1-5
        overall_rating = random.randint(1, 5)
        product_quality = random.randint(1, 5)
        customer_service = random.randint(1, 5)
        delivery_speed = random.randint(1, 5)
        would_recommend = random.randint(1, 5)
        category = random.choice(categories)
        
        satisfaction_data.append((customer_id, survey_date.date(), overall_rating,
                                product_quality, customer_service, delivery_speed,
                                would_recommend, category))
    
    cursor = conn.cursor()
    cursor.executemany('''
        INSERT INTO customer_satisfaction 
        (customer_id, survey_date, overall_rating, product_quality, customer_service, 
         delivery_speed, would_recommend, product_category)
        VALUES (?, ?, ?, ?, ?, ?, ?, ?)
    ''', satisfaction_data)
    conn.commit()
    print(f"Inserted {len(satisfaction_data)} customer satisfaction records")

def populate_financial_metrics(conn):
    """Populate financial metrics table."""
    financial_data = []
    
    # Generate quarterly data for 2 years
    for year in [2023, 2024]:
        for quarter in ['Q1', 'Q2', 'Q3', 'Q4']:
            # Generate monthly data for each quarter
            for month in range(3):
                if quarter == 'Q1':
                    month_num = month + 1
                elif quarter == 'Q2':
                    month_num = month + 4
                elif quarter == 'Q3':
                    month_num = month + 7
                else:
                    month_num = month + 10
                
                metric_date = datetime(year, month_num, 15).date()
                
                # Generate realistic financial data with some growth trend
                base_revenue = 1000000 + (year - 2023) * 200000 + month * 50000
                revenue = base_revenue + random.randint(-100000, 200000)
                
                expenses = revenue * random.uniform(0.6, 0.8)
                profit = revenue - expenses
                
                financial_data.append((metric_date, round(revenue, 2), 
                                     round(expenses, 2), round(profit, 2), quarter, year))
    
    cursor = conn.cursor()
    cursor.executemany('''
        INSERT INTO financial_metrics (metric_date, revenue, expenses, profit, quarter, year)
        VALUES (?, ?, ?, ?, ?, ?)
    ''', financial_data)
    conn.commit()
    print(f"Inserted {len(financial_data)} financial metrics records")

def main():
    """Main function to create and populate database."""
    print("Creating sample SQLite database...")
    
    conn = create_database()
    
    print("Populating tables with sample data...")
    populate_employees(conn)
    populate_sales(conn)
    populate_customer_satisfaction(conn)
    populate_financial_metrics(conn)
    
    conn.close()
    print("\nDatabase created successfully!")
    print("Database location: database/chatbot.db")
    print("\nTables created:")
    print("- employees: Employee information and performance data")
    print("- sales: Sales transactions and revenue data")
    print("- customer_satisfaction: Customer survey responses")
    print("- financial_metrics: Quarterly financial performance")

if __name__ == "__main__":
    main()
