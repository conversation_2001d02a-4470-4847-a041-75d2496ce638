"""Main Agent Orchestrator for the Agentic Chatbot."""

from typing import Dict, Any, Optional
from agents.rag_agent import get_rag_agent
from agents.sql_agent import get_sql_agent
from agents.intent_classifier import classify_query
from config.settings import settings

class MainAgent:
    """Main orchestrator agent that coordinates between RAG and SQL agents."""
    
    def __init__(self):
        """Initialize the main agent."""
        self.rag_agent = None
        self.sql_agent = None
        self.conversation_history = []
    
    def _get_rag_agent(self):
        """Lazy loading of RAG agent."""
        if self.rag_agent is None:
            self.rag_agent = get_rag_agent()
        return self.rag_agent
    
    def _get_sql_agent(self):
        """Lazy loading of SQL agent."""
        if self.sql_agent is None:
            self.sql_agent = get_sql_agent()
        return self.sql_agent
    
    def initialize_rag_system(self, force_reindex: bool = False):
        """Initialize the RAG system by indexing documents."""
        try:
            rag_agent = self._get_rag_agent()
            rag_agent.index_documents(force_reindex=force_reindex)
            return {"success": True, "message": "RAG system initialized successfully"}
        except Exception as e:
            return {"success": False, "message": f"Error initializing RAG system: {e}"}
    
    def process_query(self, query: str, user_id: Optional[str] = None) -> Dict[str, Any]:
        """Process a user query and return response."""
        try:
            # Classify the intent
            classification = classify_query(query)
            intent = classification["intent"]
            confidence = classification["confidence"]
            
            print(f"Query classified as: {intent} (confidence: {confidence:.2f})")
            print(f"Reasoning: {classification['reasoning']}")
            
            # Route to appropriate agent
            if intent == "SQL":
                response = self._handle_sql_query(query)
            else:  # RAG
                response = self._handle_rag_query(query)
            
            # Add metadata to response
            response.update({
                "intent": intent,
                "confidence": confidence,
                "reasoning": classification["reasoning"],
                "query": query,
                "timestamp": self._get_timestamp()
            })
            
            # Store in conversation history
            self._add_to_history(query, response, user_id)
            
            return response
            
        except Exception as e:
            error_response = {
                "answer": f"I encountered an error processing your query: {str(e)}",
                "success": False,
                "intent": "unknown",
                "confidence": 0.0,
                "query": query,
                "timestamp": self._get_timestamp()
            }
            self._add_to_history(query, error_response, user_id)
            return error_response
    
    def _handle_sql_query(self, query: str) -> Dict[str, Any]:
        """Handle queries that require SQL/database operations."""
        sql_agent = self._get_sql_agent()
        result = sql_agent.query(query)
        
        return {
            "answer": result["answer"],
            "agent_used": "SQL",
            "success": result["success"],
            "sql_query": result.get("sql_query"),
            "data": result.get("data", []),
            "row_count": result.get("row_count", 0)
        }
    
    def _handle_rag_query(self, query: str) -> Dict[str, Any]:
        """Handle queries that require document retrieval."""
        rag_agent = self._get_rag_agent()
        result = rag_agent.query(query)
        
        return {
            "answer": result["answer"],
            "agent_used": "RAG",
            "success": True,
            "sources": result.get("sources", []),
            "confidence": result.get("confidence", 0.0),
            "retrieved_chunks": result.get("retrieved_chunks", 0)
        }
    
    def _get_timestamp(self) -> str:
        """Get current timestamp."""
        from datetime import datetime
        return datetime.now().isoformat()
    
    def _add_to_history(self, query: str, response: Dict[str, Any], user_id: Optional[str]):
        """Add interaction to conversation history."""
        history_entry = {
            "user_id": user_id,
            "query": query,
            "response": response,
            "timestamp": response.get("timestamp")
        }
        
        self.conversation_history.append(history_entry)
        
        # Keep only last 50 interactions to manage memory
        if len(self.conversation_history) > 50:
            self.conversation_history = self.conversation_history[-50:]
    
    def get_conversation_history(self, user_id: Optional[str] = None, limit: int = 10) -> list:
        """Get conversation history for a user or all users."""
        if user_id:
            history = [entry for entry in self.conversation_history if entry["user_id"] == user_id]
        else:
            history = self.conversation_history
        
        return history[-limit:] if limit else history
    
    def get_system_status(self) -> Dict[str, Any]:
        """Get system status and health information."""
        status = {
            "system": "Agentic Chatbot",
            "version": "1.0.0",
            "status": "operational",
            "agents": {
                "rag": {"status": "ready", "initialized": self.rag_agent is not None},
                "sql": {"status": "ready", "initialized": self.sql_agent is not None}
            },
            "conversation_history_count": len(self.conversation_history),
            "settings": {
                "chat_model": settings.chat_model,
                "embedding_model": settings.embedding_model,
                "temperature": settings.temperature
            }
        }
        
        # Check RAG system status
        if self.rag_agent:
            try:
                collection_count = self.rag_agent.collection.count()
                status["agents"]["rag"]["document_count"] = collection_count
                status["agents"]["rag"]["status"] = "ready" if collection_count > 0 else "no_documents"
            except Exception as e:
                status["agents"]["rag"]["status"] = f"error: {e}"
        
        # Check SQL system status
        if self.sql_agent:
            try:
                # Test database connection
                import sqlite3
                conn = sqlite3.connect(self.sql_agent.db_path)
                cursor = conn.cursor()
                cursor.execute("SELECT COUNT(*) FROM sqlite_master WHERE type='table';")
                table_count = cursor.fetchone()[0]
                conn.close()
                
                status["agents"]["sql"]["table_count"] = table_count
                status["agents"]["sql"]["status"] = "ready" if table_count > 0 else "no_tables"
            except Exception as e:
                status["agents"]["sql"]["status"] = f"error: {e}"
        
        return status
    
    def handle_multi_turn_query(self, query: str, context: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """Handle multi-turn conversations with context."""
        # For now, treat as single query
        # Future enhancement: use context to improve responses
        return self.process_query(query)

# Global main agent instance
main_agent = None

def get_main_agent():
    """Get or create main agent instance."""
    global main_agent
    if main_agent is None:
        main_agent = MainAgent()
    return main_agent
