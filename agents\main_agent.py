"""Main Agent Orchestrator for the Agentic Chatbot."""

from typing import Dict, Any, Optional
from agents.rag_agent import get_rag_agent
from agents.fallback_rag_agent import get_fallback_rag_agent
from agents.sql_agent import get_sql_agent
from agents.intent_classifier import classify_query
from config.settings import settings

class MainAgent:
    """Main orchestrator agent that coordinates between RAG and SQL agents."""
    
    def __init__(self):
        """Initialize the main agent."""
        self.rag_agent = None
        self.sql_agent = None
        self.conversation_history = []
    
    def _get_rag_agent(self):
        """Lazy loading of RAG agent."""
        if self.rag_agent is None:
            self.rag_agent = get_rag_agent()
        return self.rag_agent
    
    def _get_sql_agent(self):
        """Lazy loading of SQL agent."""
        if self.sql_agent is None:
            self.sql_agent = get_sql_agent()
        return self.sql_agent
    
    def initialize_rag_system(self, force_reindex: bool = False):
        """Initialize the RAG system by indexing documents."""
        try:
            rag_agent = self._get_rag_agent()
            rag_agent.index_documents(force_reindex=force_reindex)
            return {"success": True, "message": "RAG system initialized successfully"}
        except Exception as e:
            return {"success": False, "message": f"Error initializing RAG system: {e}"}
    
    def process_query(self, query: str, user_id: Optional[str] = None) -> Dict[str, Any]:
        """Process a user query and return response."""
        try:
            # Classify the intent
            try:
                classification = classify_query(query)
                intent = classification["intent"]
                confidence = classification["confidence"]
                reasoning = classification["reasoning"]
            except Exception as e:
                # Fallback classification using simple keyword matching
                print(f"Intent classification failed, using fallback: {e}")
                intent, confidence, reasoning = self._simple_intent_classification(query)

            print(f"Query classified as: {intent} (confidence: {confidence:.2f})")
            print(f"Reasoning: {reasoning}")

            # Route to appropriate agent
            if intent == "SQL":
                response = self._handle_sql_query(query)
            else:  # RAG
                response = self._handle_rag_query(query)
            
            # Add metadata to response
            response.update({
                "intent": intent,
                "confidence": confidence,
                "reasoning": classification["reasoning"],
                "query": query,
                "timestamp": self._get_timestamp()
            })
            
            # Store in conversation history
            self._add_to_history(query, response, user_id)
            
            return response
            
        except Exception as e:
            error_response = {
                "answer": f"I encountered an error processing your query: {str(e)}",
                "success": False,
                "intent": "unknown",
                "confidence": 0.0,
                "query": query,
                "timestamp": self._get_timestamp()
            }
            self._add_to_history(query, error_response, user_id)
            return error_response

    def _simple_intent_classification(self, query: str) -> tuple:
        """Simple keyword-based intent classification as fallback."""
        query_lower = query.lower()

        # SQL keywords
        sql_keywords = [
            'average', 'mean', 'sum', 'total', 'count', 'how many', 'statistics',
            'revenue', 'sales', 'profit', 'salary', 'performance', 'score',
            'top', 'bottom', 'highest', 'lowest', 'compare', 'comparison',
            'trend', 'growth', 'percentage', 'ratio', 'metric', 'data',
            'employee', 'customer', 'financial', 'quarter', 'year', 'month',
            'department', 'region', 'category', 'analysis', 'report'
        ]

        # RAG keywords
        rag_keywords = [
            'what is', 'how to', 'explain', 'tell me about', 'describe',
            'policy', 'procedure', 'guide', 'manual', 'handbook',
            'benefits', 'rules', 'conduct', 'overview', 'information',
            'setup', 'install', 'configure', 'research', 'study',
            'definition', 'meaning', 'purpose', 'background', 'document'
        ]

        sql_score = sum(1 for keyword in sql_keywords if keyword in query_lower)
        rag_score = sum(1 for keyword in rag_keywords if keyword in query_lower)

        if sql_score > rag_score:
            return "SQL", 0.7, f"Keyword analysis suggests data/statistical query ({sql_score} SQL keywords vs {rag_score} RAG keywords)"
        else:
            return "RAG", 0.7, f"Keyword analysis suggests document/information query ({rag_score} RAG keywords vs {sql_score} SQL keywords)"
    
    def _handle_sql_query(self, query: str) -> Dict[str, Any]:
        """Handle queries that require SQL/database operations."""
        try:
            sql_agent = self._get_sql_agent()
            result = sql_agent.query(query)

            if result["success"]:
                return {
                    "answer": result["answer"],
                    "agent_used": "SQL",
                    "success": result["success"],
                    "sql_query": result.get("sql_query"),
                    "data": result.get("data", []),
                    "row_count": result.get("row_count", 0)
                }
            else:
                # Try simple fallback queries
                fallback_result = self._try_simple_sql_fallback(query)
                return fallback_result
        except Exception as e:
            # Try simple fallback queries
            fallback_result = self._try_simple_sql_fallback(query)
            return fallback_result

    def _try_simple_sql_fallback(self, query: str) -> Dict[str, Any]:
        """Try simple predefined SQL queries as fallback."""
        import sqlite3

        query_lower = query.lower()

        # Simple query mappings
        simple_queries = {
            "employee": "SELECT COUNT(*) as count FROM employees",
            "department": "SELECT department, COUNT(*) as count FROM employees GROUP BY department",
            "salary": "SELECT AVG(salary) as avg_salary FROM employees",
            "sales": "SELECT COUNT(*) as count FROM sales",
            "revenue": "SELECT SUM(total_amount) as total_revenue FROM sales",
            "satisfaction": "SELECT AVG(overall_rating) as avg_rating FROM customer_satisfaction"
        }

        try:
            conn = sqlite3.connect('database/chatbot.db')
            cursor = conn.cursor()

            # Find matching query
            for keyword, sql in simple_queries.items():
                if keyword in query_lower:
                    cursor.execute(sql)
                    result = cursor.fetchall()

                    if keyword == "department":
                        answer = "Department breakdown:\n"
                        for row in result:
                            answer += f"- {row[0]}: {row[1]} employees\n"
                    elif keyword == "salary":
                        answer = f"Average salary: ${result[0][0]:,.2f}"
                    elif keyword == "revenue":
                        answer = f"Total revenue: ${result[0][0]:,.2f}"
                    elif keyword == "satisfaction":
                        answer = f"Average customer satisfaction: {result[0][0]:.1f}/5"
                    else:
                        answer = f"Count: {result[0][0]}"

                    conn.close()
                    return {
                        "answer": answer,
                        "agent_used": "SQL (Simple)",
                        "success": True,
                        "sql_query": sql,
                        "data": [dict(zip([desc[0] for desc in cursor.description], row)) for row in result],
                        "row_count": len(result)
                    }

            conn.close()
            return {
                "answer": "I couldn't process that SQL query due to API limitations. Please try a simpler question about employees, departments, sales, or revenue.",
                "agent_used": "SQL (Fallback)",
                "success": False
            }

        except Exception as e:
            return {
                "answer": f"Database query failed: {str(e)}",
                "agent_used": "SQL (Error)",
                "success": False
            }
    
    def _handle_rag_query(self, query: str) -> Dict[str, Any]:
        """Handle queries that require document retrieval."""
        try:
            rag_agent = self._get_rag_agent()
            result = rag_agent.query(query)

            # Check if we got a meaningful result
            if result["answer"] and not result["answer"].startswith("I couldn't find"):
                return {
                    "answer": result["answer"],
                    "agent_used": "RAG",
                    "success": True,
                    "sources": result.get("sources", []),
                    "confidence": result.get("confidence", 0.0),
                    "retrieved_chunks": result.get("retrieved_chunks", 0)
                }
            else:
                # Fall back to text search
                print("RAG agent failed, using fallback text search...")
                fallback_agent = get_fallback_rag_agent()
                result = fallback_agent.query(query)

                return {
                    "answer": result["answer"],
                    "agent_used": "RAG (Text Search)",
                    "success": True,
                    "sources": result.get("sources", []),
                    "confidence": result.get("confidence", 0.0),
                    "retrieved_chunks": result.get("retrieved_chunks", 0),
                    "method": result.get("method", "fallback")
                }
        except Exception as e:
            # If RAG completely fails, use fallback
            print(f"RAG agent error, using fallback: {e}")
            fallback_agent = get_fallback_rag_agent()
            result = fallback_agent.query(query)

            return {
                "answer": result["answer"],
                "agent_used": "RAG (Text Search)",
                "success": True,
                "sources": result.get("sources", []),
                "confidence": result.get("confidence", 0.0),
                "retrieved_chunks": result.get("retrieved_chunks", 0),
                "method": result.get("method", "fallback")
            }
    
    def _get_timestamp(self) -> str:
        """Get current timestamp."""
        from datetime import datetime
        return datetime.now().isoformat()
    
    def _add_to_history(self, query: str, response: Dict[str, Any], user_id: Optional[str]):
        """Add interaction to conversation history."""
        history_entry = {
            "user_id": user_id,
            "query": query,
            "response": response,
            "timestamp": response.get("timestamp")
        }
        
        self.conversation_history.append(history_entry)
        
        # Keep only last 50 interactions to manage memory
        if len(self.conversation_history) > 50:
            self.conversation_history = self.conversation_history[-50:]
    
    def get_conversation_history(self, user_id: Optional[str] = None, limit: int = 10) -> list:
        """Get conversation history for a user or all users."""
        if user_id:
            history = [entry for entry in self.conversation_history if entry["user_id"] == user_id]
        else:
            history = self.conversation_history
        
        return history[-limit:] if limit else history
    
    def get_system_status(self) -> Dict[str, Any]:
        """Get system status and health information."""
        status = {
            "system": "Agentic Chatbot",
            "version": "1.0.0",
            "status": "operational",
            "agents": {
                "rag": {"status": "ready", "initialized": self.rag_agent is not None},
                "sql": {"status": "ready", "initialized": self.sql_agent is not None}
            },
            "conversation_history_count": len(self.conversation_history),
            "settings": {
                "chat_model": settings.chat_model,
                "embedding_model": settings.embedding_model,
                "temperature": settings.temperature
            }
        }
        
        # Check RAG system status
        if self.rag_agent:
            try:
                collection_count = self.rag_agent.collection.count()
                status["agents"]["rag"]["document_count"] = collection_count
                status["agents"]["rag"]["status"] = "ready" if collection_count > 0 else "no_documents"
            except Exception as e:
                status["agents"]["rag"]["status"] = f"error: {e}"
        
        # Check SQL system status
        if self.sql_agent:
            try:
                # Test database connection
                import sqlite3
                conn = sqlite3.connect(self.sql_agent.db_path)
                cursor = conn.cursor()
                cursor.execute("SELECT COUNT(*) FROM sqlite_master WHERE type='table';")
                table_count = cursor.fetchone()[0]
                conn.close()
                
                status["agents"]["sql"]["table_count"] = table_count
                status["agents"]["sql"]["status"] = "ready" if table_count > 0 else "no_tables"
            except Exception as e:
                status["agents"]["sql"]["status"] = f"error: {e}"
        
        return status
    
    def handle_multi_turn_query(self, query: str, context: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """Handle multi-turn conversations with context."""
        # For now, treat as single query
        # Future enhancement: use context to improve responses
        return self.process_query(query)

# Global main agent instance
main_agent = None

def get_main_agent():
    """Get or create main agent instance."""
    global main_agent
    if main_agent is None:
        main_agent = MainAgent()
    return main_agent
