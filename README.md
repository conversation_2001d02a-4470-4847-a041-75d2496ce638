# Agentic Chatbot with RAG and SQL Capabilities

A sophisticated chatbot that combines Retrieval-Augmented Generation (RAG) for document queries and SQL analysis for statistical questions using an agentic approach.

## Features

🤖 **Agentic Architecture**: Intelligent routing between specialized agents
📚 **RAG Pipeline**: Query documents using natural language
📊 **SQL Agent**: Statistical analysis and data queries
🎯 **Intent Classification**: Automatic routing to appropriate agent
🖥️ **Streamlit UI**: User-friendly web interface
📄 **Sample Data**: Pre-loaded with sample PDFs and database

## Architecture

```
User Query → Intent Classifier → Route to Agent → Process → Response
                    ↓
            ┌─────────────────┐
            │  RAG Agent      │  ← Document queries
            │  - PDF parsing  │
            │  - Vector DB    │
            │  - Embeddings   │
            └─────────────────┘
                    ↓
            ┌─────────────────┐
            │  SQL Agent      │  ← Statistical queries
            │  - Query gen    │
            │  - SQLite DB    │
            │  - Analytics    │
            └─────────────────┘
```

## Quick Start

### 1. Setup Environment

```bash
# Clone or download the project
cd slcb

# Install dependencies
pip install -r requirements.txt

# Create environment file
cp .env.example .env
```

### 2. Configure OpenAI API

Edit `.env` file and add your OpenAI API key:
```
OPENAI_API_KEY=your_actual_api_key_here
```

### 3. Run the Chatbot

```bash
python run_chatbot.py
```

This will:
- Check your environment setup
- Initialize the RAG system (index documents)
- Start the Streamlit web interface
- Open your browser to http://localhost:8501

## Sample Queries

### 📊 SQL/Statistical Queries
- "What is the average salary by department?"
- "How many sales were made in Q4 2024?"
- "Which region has the highest total sales?"
- "What is the customer satisfaction score for Electronics?"
- "Show me the top 5 performing employees"

### 📚 RAG/Document Queries
- "What is the company's vacation policy?"
- "How do I set up the SmartHome AI Assistant?"
- "What are the latest advances in large language models?"
- "Tell me about the company benefits package"
- "What is the code of conduct?"

## Project Structure

```
slcb/
├── agents/                 # Agent implementations
│   ├── main_agent.py      # Main orchestrator
│   ├── rag_agent.py       # RAG functionality
│   ├── sql_agent.py       # SQL analysis
│   └── intent_classifier.py # Query routing
├── config/                # Configuration
│   └── settings.py        # App settings
├── ui/                    # User interface
│   └── streamlit_app.py   # Streamlit web app
├── documents/             # Sample PDF documents
├── database/              # SQLite database
├── data/                  # Data processing scripts
├── requirements.txt       # Python dependencies
├── .env.example          # Environment template
└── run_chatbot.py        # Main startup script
```

## Sample Data

### Documents (RAG)
- **Company Handbook**: Policies, benefits, code of conduct
- **AI Research Paper**: Technical content about LLMs
- **Product Manual**: SmartHome device setup guide

### Database Tables (SQL)
- **employees**: Staff information and performance
- **sales**: Transaction and revenue data
- **customer_satisfaction**: Survey responses
- **financial_metrics**: Quarterly financial data

## Technology Stack

- **LangChain**: Agent framework and LLM integration
- **OpenAI**: GPT models for chat and embeddings
- **ChromaDB**: Vector database for document storage
- **SQLite**: Relational database for structured data
- **Streamlit**: Web interface
- **Pandas**: Data manipulation
- **PyPDF2**: PDF text extraction

## Configuration

Key settings in `config/settings.py`:
- **Models**: GPT-3.5-turbo for chat, text-embedding-ada-002 for embeddings
- **RAG**: 1000 char chunks, 200 char overlap, top 5 results
- **Database**: SQLite with sample business data

## Troubleshooting

### Common Issues

1. **Missing OpenAI API Key**
   - Ensure `.env` file exists with valid `OPENAI_API_KEY`

2. **Import Errors**
   - Install all requirements: `pip install -r requirements.txt`

3. **No Documents Found**
   - Run `python data/create_sample_pdfs.py` to create sample PDFs

4. **Database Errors**
   - Run `python database/create_sample_db.py` to recreate database

5. **ChromaDB Issues**
   - Delete `chroma_db` folder and restart to reinitialize

### Manual Setup

If automatic setup fails, run components individually:

```bash
# Create sample documents
python data/create_sample_pdfs.py

# Create sample database
python database/create_sample_db.py

# Run Streamlit directly
streamlit run ui/streamlit_app.py
```

## Extending the System

### Adding New Documents
1. Place PDF files in `documents/` folder
2. Restart the application or use "Force Reindex" in UI

### Adding Database Tables
1. Modify `database/create_sample_db.py`
2. Update SQL agent schema in `agents/sql_agent.py`

### Customizing Agents
- **RAG Agent**: Modify chunking, embedding models, retrieval logic
- **SQL Agent**: Add new query patterns, improve natural language processing
- **Intent Classifier**: Enhance classification logic, add new categories

## License

This project is for educational and demonstration purposes.
