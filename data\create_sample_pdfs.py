"""<PERSON><PERSON><PERSON> to create sample PDF documents for RAG demonstration."""

from reportlab.lib.pagesizes import letter
from reportlab.platypus import SimpleDocTemplate, Paragraph, Spacer
from reportlab.lib.styles import getSampleStyleSheet, ParagraphStyle
from reportlab.lib.units import inch
import os

def create_company_handbook():
    """Create a company handbook PDF."""
    doc = SimpleDocTemplate("documents/company_handbook.pdf", pagesize=letter)
    styles = getSampleStyleSheet()
    story = []
    
    # Title
    title_style = ParagraphStyle(
        'CustomTitle',
        parent=styles['Heading1'],
        fontSize=24,
        spaceAfter=30,
        alignment=1  # Center alignment
    )
    
    story.append(Paragraph("TechCorp Employee Handbook", title_style))
    story.append(Spacer(1, 20))
    
    # Content sections
    sections = [
        ("Company Overview", """
        TechCorp is a leading technology company founded in 2010, specializing in artificial intelligence 
        and machine learning solutions. Our mission is to democratize AI technology and make it accessible 
        to businesses of all sizes. We have over 500 employees across 10 countries and serve more than 
        1000 clients worldwide.
        """),
        
        ("Work Policies", """
        Our work policies are designed to create a flexible and productive environment:
        
        • Remote Work: Employees can work remotely up to 3 days per week
        • Flexible Hours: Core hours are 10 AM to 3 PM, with flexible start and end times
        • Vacation Policy: 25 days of paid vacation per year, plus national holidays
        • Sick Leave: Unlimited sick days with manager approval
        • Professional Development: $2000 annual budget for training and conferences
        """),
        
        ("Benefits Package", """
        TechCorp offers comprehensive benefits:
        
        • Health Insurance: 100% premium coverage for employees, 80% for family
        • Dental and Vision: Full coverage included
        • 401(k) Retirement Plan: 6% company match
        • Life Insurance: 2x annual salary coverage
        • Wellness Program: Gym membership reimbursement up to $100/month
        • Mental Health: Free counseling sessions and mental health apps
        """),
        
        ("Code of Conduct", """
        All employees must adhere to our code of conduct:
        
        • Respect and Inclusion: Treat all colleagues with respect regardless of background
        • Confidentiality: Protect company and client information
        • Integrity: Act honestly and ethically in all business dealings
        • Safety: Maintain a safe work environment for everyone
        • Compliance: Follow all applicable laws and regulations
        """)
    ]
    
    for title, content in sections:
        story.append(Paragraph(title, styles['Heading2']))
        story.append(Spacer(1, 12))
        story.append(Paragraph(content, styles['Normal']))
        story.append(Spacer(1, 20))
    
    doc.build(story)
    print("Created: documents/company_handbook.pdf")

def create_ai_research_paper():
    """Create an AI research paper PDF."""
    doc = SimpleDocTemplate("documents/ai_research_paper.pdf", pagesize=letter)
    styles = getSampleStyleSheet()
    story = []
    
    # Title
    title_style = ParagraphStyle(
        'CustomTitle',
        parent=styles['Heading1'],
        fontSize=20,
        spaceAfter=30,
        alignment=1
    )
    
    story.append(Paragraph("Advances in Large Language Models: A Comprehensive Survey", title_style))
    story.append(Spacer(1, 20))
    
    sections = [
        ("Abstract", """
        Large Language Models (LLMs) have revolutionized natural language processing and artificial 
        intelligence. This paper provides a comprehensive survey of recent advances in LLM architecture, 
        training methodologies, and applications. We examine the evolution from transformer-based models 
        to current state-of-the-art systems, analyzing their capabilities, limitations, and future directions.
        """),
        
        ("Introduction", """
        The field of natural language processing has witnessed unprecedented growth with the advent of 
        large language models. Starting with the transformer architecture introduced by Vaswani et al. 
        in 2017, we have seen remarkable progress in model capabilities. GPT-3, released in 2020, 
        demonstrated emergent abilities in few-shot learning, while more recent models like GPT-4 and 
        Claude have shown even more sophisticated reasoning capabilities.
        """),
        
        ("Model Architectures", """
        Modern LLMs primarily build upon the transformer architecture with several key innovations:
        
        • Attention Mechanisms: Multi-head self-attention allows models to focus on relevant parts of input
        • Positional Encoding: Enables understanding of sequence order without recurrent connections
        • Layer Normalization: Stabilizes training and improves convergence
        • Feed-Forward Networks: Provide non-linear transformations between attention layers
        
        Recent architectures have explored modifications like sparse attention, mixture of experts, 
        and retrieval-augmented generation to improve efficiency and capabilities.
        """),
        
        ("Training Methodologies", """
        Training large language models involves several sophisticated techniques:
        
        • Pre-training: Unsupervised learning on massive text corpora
        • Fine-tuning: Task-specific adaptation using supervised learning
        • Reinforcement Learning from Human Feedback (RLHF): Aligning models with human preferences
        • Constitutional AI: Training models to be helpful, harmless, and honest
        
        The scale of training has grown exponentially, with models now trained on trillions of tokens 
        using thousands of GPUs or TPUs.
        """),
        
        ("Applications and Impact", """
        LLMs have found applications across numerous domains:
        
        • Content Generation: Writing, coding, and creative tasks
        • Question Answering: Information retrieval and knowledge synthesis
        • Language Translation: Cross-lingual communication
        • Code Generation: Automated programming assistance
        • Scientific Research: Literature review and hypothesis generation
        
        The societal impact includes both opportunities for productivity enhancement and challenges 
        related to misinformation, bias, and job displacement.
        """),
        
        ("Future Directions", """
        Several research directions show promise for advancing LLM capabilities:
        
        • Multimodal Integration: Combining text, image, and audio processing
        • Improved Reasoning: Enhanced logical and mathematical capabilities
        • Efficiency Optimization: Reducing computational requirements
        • Alignment Research: Better understanding and control of model behavior
        • Specialized Models: Domain-specific optimization for particular use cases
        """)
    ]
    
    for title, content in sections:
        story.append(Paragraph(title, styles['Heading2']))
        story.append(Spacer(1, 12))
        story.append(Paragraph(content, styles['Normal']))
        story.append(Spacer(1, 20))
    
    doc.build(story)
    print("Created: documents/ai_research_paper.pdf")

def create_product_manual():
    """Create a product manual PDF."""
    doc = SimpleDocTemplate("documents/product_manual.pdf", pagesize=letter)
    styles = getSampleStyleSheet()
    story = []
    
    title_style = ParagraphStyle(
        'CustomTitle',
        parent=styles['Heading1'],
        fontSize=22,
        spaceAfter=30,
        alignment=1
    )
    
    story.append(Paragraph("SmartHome AI Assistant - User Manual", title_style))
    story.append(Spacer(1, 20))
    
    sections = [
        ("Getting Started", """
        Welcome to your SmartHome AI Assistant! This device uses advanced artificial intelligence 
        to help manage your home automation, answer questions, and provide personalized assistance.
        
        What's in the box:
        • SmartHome AI Assistant device
        • Power adapter
        • Quick start guide
        • Warranty information
        """),
        
        ("Setup Instructions", """
        Follow these steps to set up your device:
        
        1. Connect the power adapter to your device and plug into wall outlet
        2. Download the SmartHome app from your device's app store
        3. Open the app and select "Add New Device"
        4. Follow the on-screen instructions to connect to your Wi-Fi network
        5. Complete the voice training by speaking the provided phrases
        6. Your device is now ready to use!
        
        The setup process typically takes 5-10 minutes.
        """),
        
        ("Voice Commands", """
        Your AI Assistant responds to natural language commands. Here are some examples:
        
        Home Control:
        • "Turn on the living room lights"
        • "Set the temperature to 72 degrees"
        • "Lock the front door"
        • "Start the coffee maker"
        
        Information Queries:
        • "What's the weather like today?"
        • "What's on my calendar?"
        • "Play my morning playlist"
        • "Set a timer for 10 minutes"
        
        Smart Features:
        • "Good morning" - Activates morning routine
        • "I'm leaving" - Activates away mode
        • "Goodnight" - Activates sleep mode
        """),
        
        ("Troubleshooting", """
        Common issues and solutions:
        
        Device not responding:
        • Check power connection
        • Ensure Wi-Fi connectivity
        • Try saying "Hey Assistant, restart"
        
        Voice not recognized:
        • Speak clearly and at normal volume
        • Reduce background noise
        • Retrain voice model in app settings
        
        Smart devices not connecting:
        • Verify devices are compatible
        • Check device-specific setup instructions
        • Restart both devices if needed
        
        For additional support, visit our website or contact customer service.
        """),
        
        ("Privacy and Security", """
        Your privacy is important to us:
        
        • Voice data is processed locally when possible
        • Personal information is encrypted and secure
        • You can delete voice recordings anytime
        • Regular security updates are automatically installed
        • Data sharing preferences can be customized in settings
        
        The device includes a physical mute button for complete privacy when needed.
        """)
    ]
    
    for title, content in sections:
        story.append(Paragraph(title, styles['Heading2']))
        story.append(Spacer(1, 12))
        story.append(Paragraph(content, styles['Normal']))
        story.append(Spacer(1, 20))
    
    doc.build(story)
    print("Created: documents/product_manual.pdf")

if __name__ == "__main__":
    # Create documents directory if it doesn't exist
    os.makedirs("documents", exist_ok=True)
    
    # Create sample PDFs
    create_company_handbook()
    create_ai_research_paper()
    create_product_manual()
    
    print("\nAll sample PDF documents created successfully!")
    print("Documents created:")
    print("- documents/company_handbook.pdf")
    print("- documents/ai_research_paper.pdf") 
    print("- documents/product_manual.pdf")
