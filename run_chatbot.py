"""Main script to run the Agentic Chatbot."""

import os
import sys
import subprocess
from pathlib import Path

def check_environment():
    """Check if environment is properly set up."""
    print("Checking environment setup...")
    
    # Check if .env file exists
    if not os.path.exists('.env'):
        print("❌ .env file not found!")
        print("Please create a .env file based on .env.example and add your OpenAI API key.")
        return False
    
    # Check if OpenAI API key is set
    from dotenv import load_dotenv
    load_dotenv()
    
    api_key = os.getenv('OPENAI_API_KEY')
    if not api_key or api_key == 'your_openai_api_key_here':
        print("❌ OpenAI API key not set!")
        print("Please set your OPENAI_API_KEY in the .env file.")
        return False
    
    print("✅ Environment setup looks good!")
    return True

def check_dependencies():
    """Check if required dependencies are installed."""
    print("Checking dependencies...")
    
    required_packages = [
        'streamlit', 'langchain', 'langchain-openai', 'chromadb',
        'sentence-transformers', 'pandas', 'PyPDF2', 'reportlab'
    ]
    
    missing_packages = []
    
    for package in required_packages:
        try:
            __import__(package.replace('-', '_'))
        except ImportError:
            missing_packages.append(package)
    
    if missing_packages:
        print(f"❌ Missing packages: {', '.join(missing_packages)}")
        print("Please install missing packages using:")
        print(f"pip install {' '.join(missing_packages)}")
        return False
    
    print("✅ All dependencies are installed!")
    return True

def setup_directories():
    """Create necessary directories."""
    print("Setting up directories...")
    
    directories = ['documents', 'database', 'chroma_db']
    
    for directory in directories:
        Path(directory).mkdir(exist_ok=True)
    
    print("✅ Directories created!")

def initialize_system():
    """Initialize the chatbot system."""
    print("Initializing chatbot system...")
    
    try:
        # Import and initialize main agent
        from agents.main_agent import get_main_agent
        
        main_agent = get_main_agent()
        
        # Initialize RAG system
        print("Initializing RAG system...")
        result = main_agent.initialize_rag_system()
        
        if result["success"]:
            print("✅ RAG system initialized successfully!")
        else:
            print(f"⚠️ RAG system initialization warning: {result['message']}")
        
        # Test SQL system
        print("Testing SQL system...")
        sql_agent = main_agent._get_sql_agent()
        test_result = sql_agent.query("SELECT COUNT(*) as table_count FROM sqlite_master WHERE type='table'")
        
        if test_result["success"]:
            print("✅ SQL system is working!")
        else:
            print(f"❌ SQL system error: {test_result['answer']}")
        
        print("✅ System initialization complete!")
        return True
        
    except Exception as e:
        print(f"❌ Error during initialization: {e}")
        return False

def run_streamlit():
    """Run the Streamlit application."""
    print("Starting Streamlit application...")
    print("🚀 Your chatbot will open in your web browser!")
    print("📝 You can ask questions about documents or request data analysis.")
    print("🔗 The app will be available at: http://localhost:8501")
    print("\nPress Ctrl+C to stop the application.\n")
    
    try:
        subprocess.run([
            sys.executable, "-m", "streamlit", "run", 
            "ui/streamlit_app.py",
            "--server.port=8501",
            "--server.address=localhost"
        ])
    except KeyboardInterrupt:
        print("\n👋 Chatbot stopped. Goodbye!")

def main():
    """Main function to run the chatbot."""
    print("=" * 60)
    print("🤖 AGENTIC CHATBOT STARTUP")
    print("=" * 60)
    
    # Check environment
    if not check_environment():
        return
    
    # Check dependencies
    if not check_dependencies():
        return
    
    # Setup directories
    setup_directories()
    
    # Initialize system
    if not initialize_system():
        print("❌ System initialization failed. Please check the errors above.")
        return
    
    # Run Streamlit app
    run_streamlit()

if __name__ == "__main__":
    main()
